# HTTP 客户端使用指南

## 快速开始

### 1. 创建客户端

```java
import cn.bluesking.nanfeng.tools.common.http.ApiClient;
import cn.bluesking.nanfeng.tools.common.http.ApiClientFactory;

// 使用默认配置创建客户端
ApiClient client = ApiClientFactory.create();

// 使用自定义配置创建客户端
ApiClient client = ApiClientFactory.create(config -> {
    config.baseUrl("https://api.example.com");
    config.connectTimeout(Duration.ofSeconds(10));
    config.readTimeout(Duration.ofSeconds(30));
    config.enableLogging(true);
});
```

### 2. 发送 GET 请求

```java
// 简单 GET 请求
Mono<String> response = client.request()
    .uri("/api/users")
    .get()
    .retrieveString();

// 解析为 JSON 对象
Mono<User[]> users = client.request()
    .uri("/api/users")
    .get()
    .retrieveJson(User[].class);

// 添加请求头
Mono<String> response = client.request()
    .uri("/api/protected")
    .get()
    .header("Authorization", "Bearer " + token)
    .header("Accept", "application/json")
    .retrieveString();
```

### 3. 发送 POST 请求

```java
// 发送 JSON 数据
User newUser = new User("张三", "<EMAIL>");
Mono<User> createdUser = client.request()
    .uri("/api/users")
    .post()
    .bodyJson(newUser)
    .retrieveJson(User.class);

// 发送表单数据
Mono<LoginResponse> loginResult = client.request()
    .uri("/api/login")
    .post()
    .formData(params -> {
        params.put("username", "admin");
        params.put("password", "secret123");
    })
    .retrieveJson(LoginResponse.class);

// 发送文本数据
Mono<String> response = client.request()
    .uri("/api/webhook")
    .post()
    .bodyText("Hello, World!")
    .header("Content-Type", "text/plain")
    .retrieveString();
```

## 代理配置

### 1. HTTP 代理

```java
// 简单 HTTP 代理
client.request()
    .uri("/api/data")
    .proxy("proxy.company.com", 8080)
    .get()
    .retrieveString();

// 带认证的 HTTP 代理
client.request()
    .uri("/api/secure-data")
    .proxy("proxy.company.com", 8080, "username", "password")
    .get()
    .retrieveString();
```

### 2. SOCKS 代理

```java
// SOCKS 代理
client.request()
    .uri("/api/data")
    .proxy("socks-proxy.company.com", 1080)
    .proxyType(ApiClient.ProxyType.SOCKS)
    .get()
    .retrieveString();
```

### 3. 禁用代理

```java
// 即使配置了默认代理，也直接连接
client.request()
    .uri("http://internal-api.company.com/data")
    .noProxy()
    .get()
    .retrieveString();
```

## 高级功能

### 1. 自定义响应解析

```java
// 使用自定义解析器
Mono<CustomData> result = client.request()
    .uri("/api/custom-format")
    .get()
    .retrieve(responseBody -> {
        // 自定义解析逻辑
        return parseCustomFormat(responseBody);
    });

// 使用泛型响应解析器
GenericResponseParser<List<User>> parser = new GenericResponseParser<>(
    new TypeReference<List<User>>() {}
);
Mono<List<User>> users = client.request()
    .uri("/api/users")
    .get()
    .retrieve(parser);
```

### 2. 错误处理

```java
client.request()
    .uri("/api/users")
    .get()
    .retrieveJson(User[].class)
    .doOnSuccess(users -> {
        logger.info("获取到 {} 个用户", users.length);
    })
    .doOnError(error -> {
        logger.error("请求失败：{}", error.getMessage());
    })
    .onErrorReturn(new User[0]) // 错误时返回空数组
    .subscribe();
```

### 3. 禁用日志记录

```java
// 对于敏感请求，禁用日志记录
client.request()
    .uri("/api/sensitive-data")
    .get()
    .disableLogging()
    .header("Authorization", "Bearer " + sensitiveToken)
    .retrieveString();
```

### 4. 请求重试

```java
client.request()
    .uri("/api/unstable-service")
    .get()
    .retrieveString()
    .retry(3) // 失败时重试 3 次
    .timeout(Duration.ofSeconds(30)) // 30 秒超时
    .subscribe();
```

## 配置选项

### 1. 全局配置

```java
ApiClient client = ApiClientFactory.create(config -> {
    // 基础 URL
    config.baseUrl("https://api.example.com");
    
    // 超时设置
    config.connectTimeout(Duration.ofSeconds(10));
    config.readTimeout(Duration.ofSeconds(30));
    config.writeTimeout(Duration.ofSeconds(30));
    
    // 日志配置
    config.enableLogging(true);
    
    // 默认请求头
    config.defaultHeader("User-Agent", "MyApp/1.0");
    config.defaultHeader("Accept", "application/json");
    
    // 代理配置
    config.proxy("proxy.company.com", 8080, "username", "password");
    
    // SSL 配置
    config.trustAllCertificates(false); // 生产环境建议设为 false
});
```

### 2. 请求级配置

```java
client.request()
    .uri("/api/special-endpoint")
    .proxy("special-proxy.com", 8080) // 覆盖默认代理
    .get()
    .header("Special-Header", "value") // 添加特殊请求头
    .disableLogging() // 禁用此请求的日志
    .retrieveString();
```

## 最佳实践

### 1. 客户端复用

```java
// 推荐：创建单例客户端并复用
@Component
public class ApiService {
    private final ApiClient client;
    
    public ApiService() {
        this.client = ApiClientFactory.create(config -> {
            config.baseUrl("https://api.example.com");
            config.enableLogging(true);
        });
    }
    
    public Mono<User> getUser(Long id) {
        return client.request()
            .uri("/users/" + id)
            .get()
            .retrieveJson(User.class);
    }
}
```

### 2. 错误处理策略

```java
public Mono<List<User>> getUsersWithFallback() {
    return client.request()
        .uri("/api/users")
        .get()
        .retrieveJson(User[].class)
        .map(Arrays::asList)
        .onErrorResume(error -> {
            logger.warn("获取用户列表失败，使用缓存数据：{}", error.getMessage());
            return getCachedUsers();
        });
}
```

### 3. 响应式组合

```java
public Mono<UserProfile> getUserProfile(Long userId) {
    Mono<User> userMono = client.request()
        .uri("/users/" + userId)
        .get()
        .retrieveJson(User.class);
        
    Mono<List<Order>> ordersMono = client.request()
        .uri("/users/" + userId + "/orders")
        .get()
        .retrieveJson(Order[].class)
        .map(Arrays::asList);
        
    return Mono.zip(userMono, ordersMono)
        .map(tuple -> new UserProfile(tuple.getT1(), tuple.getT2()));
}
```

## 注意事项

### 1. 线程安全
- `ApiClient` 实例是线程安全的，可以在多线程环境中共享使用
- 建议创建单例客户端并在应用中复用

### 2. 资源管理
- 客户端会自动管理连接池和资源
- 应用关闭时，客户端会自动清理资源

### 3. 性能优化
- 复用客户端实例以利用连接池
- 合理设置超时时间
- 对于大文件传输，考虑使用流式处理

### 4. 安全考虑
- 生产环境中不要禁用 SSL 证书验证
- 敏感请求使用 `disableLogging()` 避免泄露信息
- 代理认证信息要妥善保管
