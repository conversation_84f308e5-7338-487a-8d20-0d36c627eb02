# Jasypt 配置指南

## 1. 简介

Jasypt (Java Simplified Encryption) 是一个 Java 库，可用于加密配置文件中的敏感信息，如数据库密码、API 密钥等。结合 Spring Boot 使用，可以在运行时自动解密这些信息。

本项目使用 Jasypt 对配置文件中的敏感信息进行加密，防止敏感信息泄露。

## 2. 加密配置信息

### 2.1 使用 JasyptUtils 工具类

项目中提供了 `JasyptUtils` 工具类，可以用来加密和解密配置信息：

```java
package cn.bluesking.nanfeng.tools.util;

public class JasyptUtils {
    // ... 类实现

    public static void main(String[] args) {
        // 加密密钥
        String password = "你的加密密钥";
        
        // 需要加密的敏感信息
        String plainText = "需要加密的敏感信息";
        
        String encrypted = encrypt(plainText, password);
        System.out.println("原文: " + plainText);
        System.out.println("密文: ENC(" + encrypted + ")");
        System.out.println("验证: " + decrypt(encrypted, password));
    }
}
```

运行此工具类的 main 方法，可以得到加密后的字符串。

### 2.2 使用 Gradle 命令

```bash
./gradlew bootRun --args="--jasypt.encryptor.password=你的加密密钥 --jasypt.encryptor.algorithm=PBEWithMD5AndDES --jasypt.encryptor.tool=true --jasypt.encryptor.plain-text=需要加密的敏感信息"
```

## 3. 在配置文件中使用加密信息

加密后的信息在配置文件中使用 `ENC()` 包裹，例如：

```properties
# 明文配置
database.password=my-secret-password

# 加密配置
database.password=ENC(G6N718UuyPE5bHyWKyuLQhYQvFoYkl3I)
```

## 4. 应用启动方式

### 4.1 命令行启动

```bash
java -Djasypt.encryptor.password=你的加密密钥 -jar nanfeng-tools-2.1.2.jar
```

### 4.2 使用启动脚本

#### 4.2.1 Linux/Mac:
```bash
./start.sh [加密密钥]
```

#### 4.2.2 Windows:
```batch
start.bat [加密密钥]
```

如果不提供加密密钥参数，将使用脚本中默认的密钥。

### 4.3 Docker 容器启动

#### 4.3.1 使用 Docker 运行脚本

##### Linux/Mac:
```bash
./docker-run.sh --jasypt-password=你的加密密钥
```

##### Windows:
```batch
docker-run.bat --jasypt-password=你的加密密钥
```

#### 4.3.2 手动 Docker 启动
```bash
docker run -d \
  --name nanfeng-tools \
  -p 8080:8080 \
  -e JASYPT_ENCRYPTOR_PASSWORD=你的加密密钥 \
  -v ./config:/app/config \
  -v ./data:/app/data \
  -v ./logs:/app/logs \
  maven.bluesking.cn/nanfeng-tools:2.1.2
```

## 5. 安全注意事项

1. **加密密钥管理**：
   - 生产环境中，不要在代码或配置文件中硬编码加密密钥
   - 考虑使用环境变量、密钥管理系统或安全的配置服务器存储密钥

2. **密钥传递**：
   - 避免在命令行参数中直接传递密钥（可能会被进程列表看到）
   - 优先使用环境变量传递密钥

3. **配置文件权限**：
   - 确保配置文件具有适当的访问权限
   - 仅允许必要的用户和进程访问配置文件

4. **定期更换密钥**：
   - 定期更换加密密钥和重新加密敏感信息
   - 实施密钥轮换策略

## 6. 故障排除

### 6.1 应用无法解密配置

可能原因：
- 加密密钥不正确
- 加密算法不匹配
- 加密字符串格式不正确

解决方法：
- 确保使用正确的密钥启动应用
- 确保配置文件中使用了正确的 `ENC()` 格式
- 尝试手动验证加密和解密过程

### 6.2 不同环境间的配置移植问题

如果在不同环境（开发、测试、生产）使用不同的加密密钥，需要为每个环境重新加密配置信息。

## 7. 最佳实践

### 7.1 密钥管理策略

- 开发环境：可使用团队共享的开发密钥
- 测试环境：使用专用测试环境密钥
- 生产环境：使用高强度密钥，仅运维人员访问

### 7.2 配置文件组织

- 将敏感配置集中在单独的属性文件中
- 使用 Spring 的 `@PropertySource` 加载这些文件
- 考虑对不同环境使用不同的配置文件

## 8. 参考资料

- [Jasypt 官方文档](http://www.jasypt.org/)
- [jasypt-spring-boot 项目](https://github.com/ulisesbocchio/jasypt-spring-boot)
- [Spring Boot 配置加密最佳实践](https://docs.spring.io/spring-boot/docs/current/reference/html/howto.html#howto.properties-and-configuration.encrypt-properties)

---

_最后更新：2025-05-29_ 