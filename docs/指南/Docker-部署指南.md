# Docker 部署指南

## 1. 简介

本文档提供了使用私有 Docker 仓库和 Docker Compose 部署 nanfeng-tools 应用的详细说明。

## 2. 部署流程概述

1. 开发环境：构建应用并基于明确的版本号打包 Docker 镜像，推送到私有仓库
2. 服务器环境：从私有仓库拉取指定版本镜像，使用 Docker Compose 运行容器

## 3. 开发环境操作

### 3.1 环境要求

- Docker 20.10.0 或更高版本
- Docker Compose 2.0.0 或更高版本
- JDK 17
- Gradle 7.0+ 或自动生成的 Gradle Wrapper
- 对私有仓库 docker.bluesking.cn 的访问权限

### 3.2 Gradle Wrapper 设置

项目使用 Gradle 进行构建。如果项目中缺少 Gradle Wrapper（即没有 `gradlew` 文件），可以使用以下两种方法解决：

#### 3.2.1 方法一：使用提供的生成脚本

```bash
# 赋予脚本执行权限
chmod +x generate-wrapper.sh

# 执行生成脚本
./generate-wrapper.sh
```

#### 3.2.2 方法二：使用已安装的 Gradle

如果系统已安装 Gradle，构建脚本会自动检测并使用系统 Gradle：

```bash
# 检查是否已安装 Gradle
gradle --version

# 如果需要生成 Wrapper
gradle wrapper --gradle-version=7.6.2
```

> **注意**：构建脚本会自动检测 Gradle Wrapper 是否存在，如果不存在则尝试使用系统安装的 Gradle。

### 3.3 环境变量配置

为简化 Docker 仓库认证过程，系统使用 `docker-env.sh` 环境变量配置文件存储仓库凭据：

1. 创建或编辑 `docker-env.sh` 文件（此文件不应提交到版本控制系统）：

```bash
# 创建环境变量配置文件
cp docker-env.sh.example docker-env.sh
# 编辑文件填入仓库凭据
vim docker-env.sh
```

2. 配置文件示例：

```bash
#!/bin/bash

# Docker 仓库凭据配置
export DOCKER_REGISTRY="docker.bluesking.cn"
export DOCKER_USERNAME="your_username"
export DOCKER_PASSWORD="your_password"

# 项目配置
export PROJECT_NAME="nanfeng-tools"
```

> **安全注意事项**：确保 `docker-env.sh` 已添加到 `.gitignore` 文件，避免凭据被意外提交。

构建和部署脚本会自动加载此配置文件，使用其中的环境变量进行 Docker 仓库认证。

### 3.4 构建和推送镜像

系统会优先使用命令行参数作为版本号，如果未提供参数则尝试从 `build.gradle` 文件中读取版本号：

```bash
# 赋予脚本执行权限
chmod +x build-and-push.sh

# 使用 build.gradle 中的版本号构建并推送镜像（如果能成功读取）
./build-and-push.sh

# 或明确指定版本标签（推荐方式）
./build-and-push.sh 1.2.3
```

> **注意**：如果没有提供命令行参数，且无法从 build.gradle 中读取版本号，脚本将报错并终止执行。

这个脚本会执行以下操作：
1. 加载 docker-env.sh 环境变量配置
2. 确定版本号（命令行参数优先，其次是 build.gradle 中的版本号）
3. 检测并选择适当的构建工具（Gradle Wrapper 或系统 Gradle）
4. 构建指定版本标签的镜像
5. 使用配置的凭据登录到私有仓库
6. 推送镜像到私有仓库
7. 登出私有仓库

> **重要**：脚本只会构建和推送指定版本号的镜像，不再构建和推送 latest 标签的镜像，以避免不同环境间的不确定性。

### 3.5 版本管理说明

在 `build.gradle` 文件中定义版本号的示例：

```groovy
// 项目版本定义
version = '1.2.3'
```

## 4. 服务器环境操作

### 4.1 环境要求

- Docker 20.10.0 或更高版本
- Docker Compose 2.0.0 或更高版本
- 对私有仓库 docker.bluesking.cn 的访问权限
- 至少 2GB 内存和 1 核 CPU
- 至少 10GB 可用磁盘空间

### 4.2 初始部署

1. 创建项目部署目录：

```bash
mkdir -p /opt/nanfeng-tools
cd /opt/nanfeng-tools
```

2. 上传以下文件到服务器：

- docker-compose.yml（配置为从私有仓库拉取镜像）
- deploy.sh（部署脚本）
- docker-env.sh（环境变量配置，包含仓库凭据）
- config/application.properties（应用配置文件）
- config/logback-spring.xml（日志配置文件）

3. 赋予脚本执行权限并执行部署：

```bash
chmod +x deploy.sh docker-env.sh

# 部署指定版本（必须提供版本号）
./deploy.sh 1.2.3
```

> **注意**：部署脚本必须提供版本号参数，否则将报错并终止执行。

这个脚本会执行以下操作：
1. 加载 docker-env.sh 环境变量配置
2. 验证提供了版本号参数
3. 创建必要的目录结构
4. 使用配置的凭据登录到私有仓库
5. 拉取指定版本的镜像
6. 更新 docker-compose.yml 中的镜像标签
7. 停止现有容器（如果有）
8. 使用 Docker Compose 启动服务
9. 登出私有仓库

### 4.3 版本回滚

如果需要回滚到之前的版本：

```bash
./deploy.sh 1.2.2  # 回滚到特定版本
```

### 4.4 验证部署

部署完成后，检查容器状态和使用的镜像版本：

```bash
docker-compose ps
docker-compose logs
docker inspect nanfeng-tools | grep Image
```

## 5. 配置和数据

### 5.1 目录结构

部署后的目录结构如下：

```
/opt/nanfeng-tools/
├── docker-compose.yml    # Docker Compose 配置文件
├── deploy.sh             # 部署脚本
├── docker-env.sh         # 环境变量配置（包含仓库凭据）
├── config/               # 挂载到容器 /app/config 的配置目录
│   ├── application.properties
│   └── logback-spring.xml
├── data/                 # 挂载到容器 /app/data 的数据目录
│   └── sqlite-data.db
└── logs/                 # 挂载到容器 /app/logs 的日志目录
```

### 5.2 维护操作

#### 5.2.1 更新应用版本

当需要部署新版本时：

1. 在开发环境构建并推送新版本镜像
2. 在服务器上执行部署脚本并指定新版本：

```bash
./deploy.sh 1.2.3  # 明确指定新版本号
```

#### 5.2.2 查看当前部署的版本

```bash
docker inspect nanfeng-tools --format='{{.Config.Image}}'
```

#### 5.2.3 配置更新

修改 `config` 目录中的配置文件，然后重启容器：

```bash
docker-compose restart
```

#### 5.2.4 查看日志

```bash
# 查看容器日志
docker-compose logs -f

# 查看应用日志文件
ls -la logs/
```

#### 5.2.5 数据备份

备份应用数据：

```bash
# 备份数据目录
tar -czvf nanfeng-tools-data-$(date +%Y%m%d).tar.gz data/

# 备份配置目录
tar -czvf nanfeng-tools-config-$(date +%Y%m%d).tar.gz config/
```

## 6. 故障排除

### 6.1 Gradle 构建问题

如果遇到 Gradle 构建问题：

```bash
# 检查 Gradle Wrapper 是否存在
ls -la gradlew

# 如果不存在，生成 Wrapper
./generate-wrapper.sh

# 或使用系统 Gradle
gradle clean build -x test
```

### 6.2 容器无法启动

检查日志和镜像拉取状态：

```bash
docker-compose logs
```

### 6.3 私有仓库认证问题

检查 docker-env.sh 文件中的凭据是否正确：

```bash
cat docker-env.sh  # 查看凭据配置
```

如果需要手动登录：

```bash
docker login docker.bluesking.cn
```

### 6.4 版本参数问题

如果收到版本参数相关的错误：

- 对于构建脚本：确保命令行提供了版本参数，或 build.gradle 文件包含有效的版本号
- 对于部署脚本：必须提供版本号参数

### 6.5 应用无法访问

检查网络和端口：

```bash
# 检查容器是否运行
docker ps

# 检查端口是否监听
netstat -tunlp | grep 8080

# 检查容器健康状态
docker inspect --format='{{.State.Health.Status}}' nanfeng-tools
```

## 7. 安全建议

1. 使用非 root 用户运行容器（已在 Dockerfile 中配置）
2. 定期更新镜像，修复安全漏洞
3. 对私有仓库凭据进行安全管理：
   - 确保 docker-env.sh 添加到 .gitignore
   - 设置适当的文件权限：`chmod 600 docker-env.sh`
   - 考虑使用密钥管理工具而非明文存储密码
4. 不要在脚本中硬编码敏感信息
5. 限制容器的资源使用（在 docker-compose.yml 中配置）
6. 始终使用明确的版本标签，严格避免使用 latest

## 8. 参考资料

### 8.1 环境变量参考

| 变量名 | 说明 | 默认值 |
|-------|------|--------|
| DOCKER_REGISTRY | Docker 仓库地址 | docker.bluesking.cn |
| DOCKER_USERNAME | Docker 仓库用户名 | - |
| DOCKER_PASSWORD | Docker 仓库密码 | - |
| PROJECT_NAME | 项目名称 | nanfeng-tools |
| JAVA_OPT | JVM 参数 | -Xms512m -Xmx2048m -Duser.timezone=Asia/Shanghai |
| SPRINGBOOT_OPT | Spring Boot 启动参数 | --spring.profiles.active=prod |

### 8.2 相关链接

- [Docker 官方文档](https://docs.docker.com/)
- [Docker Compose 官方文档](https://docs.docker.com/compose/)

---

_最后更新：2025-05-29_ 