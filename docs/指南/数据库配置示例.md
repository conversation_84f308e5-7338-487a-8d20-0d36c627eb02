# 数据库配置示例

## PublicDbConfig 配置说明

`PublicDbConfig` 使用 Spring Boot 的配置绑定机制，完全避免硬编码：
- 使用 `JpaProperties` 和 `HibernateProperties` 获取标准配置
- 使用 `PublicJpaProperties` 支持 public 数据源特定配置
- 通过 Reflections 自动扫描和收集 JPA Repository 包

## 配置文件示例

### application.properties 示例

```properties
# 公共数据库数据源配置
spring.datasource.public.url=***********************************************
spring.datasource.public.username=nanfeng_user
spring.datasource.public.driver-class-name=org.postgresql.Driver
spring.datasource.public.password=ENC(encrypted_password)

# 全局 JPA 配置（作为默认值）
spring.jpa.generate-ddl=false
spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy

# Public 数据源特定配置（覆盖全局配置）
spring.jpa.public.generate-ddl=false
spring.jpa.public.show-sql=false
spring.jpa.public.database-platform=org.hibernate.dialect.PostgreSQLDialect

# Public 数据源的 Hibernate 配置
spring.jpa.public.hibernate.ddl-auto=update
spring.jpa.public.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.public.hibernate.show-sql=false
spring.jpa.public.hibernate.format-sql=true

# Public 数据源的命名策略
spring.jpa.public.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
spring.jpa.public.hibernate.naming.implicit-strategy=org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl

# Public 数据源的性能配置
spring.jpa.public.hibernate.connection.pool-size=20
spring.jpa.public.hibernate.jdbc.batch-size=25
spring.jpa.public.hibernate.jdbc.fetch-size=50

# Public 数据源的缓存配置
spring.jpa.public.hibernate.cache.use-second-level-cache=true
spring.jpa.public.hibernate.cache.use-query-cache=true

# 自定义 Hibernate 属性（通过 spring.jpa.public.properties.* 前缀）
spring.jpa.public.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.public.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.public.properties.hibernate.connection.characterEncoding=utf8
spring.jpa.public.properties.hibernate.connection.useUnicode=true
```

### application.yml 示例

```yaml
spring:
  datasource:
    public:
      url: ***********************************************
      username: nanfeng_user
      password: ENC(encrypted_password)
      driver-class-name: org.postgresql.Driver
      
  jpa:
    public:
      # JPA 基础配置
      generate-ddl: false
      show-sql: false
      database-platform: org.hibernate.dialect.PostgreSQLDialect
      
      # Hibernate 配置
      hibernate:
        ddl-auto: update
        dialect: org.hibernate.dialect.PostgreSQLDialect
        show-sql: false
        format-sql: true
        naming:
          physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        connection:
          pool-size: 20
        jdbc:
          batch-size: 25
          fetch-size: 50
        cache:
          use-second-level-cache: true
          region:
            factory_class: org.hibernate.cache.jcache.JCacheRegionFactory
          use-query-cache: true
          
      # 自定义属性
      properties:
        hibernate:
          temp:
            use_jdbc_metadata_defaults: false
          jdbc:
            lob:
              non_contextual_creation: true
          connection:
            characterEncoding: utf8
            useUnicode: true
```

## 配置项说明

### VendorAdapter 配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `spring.jpa.public.generate-ddl` | 是否自动生成 DDL | `false` |
| `spring.jpa.public.show-sql` | 是否显示 SQL 语句 | `false` |
| `spring.jpa.public.database-platform` | 数据库平台/方言 | 自动检测 |

### Hibernate 核心配置

| 配置项 | 说明 | 可选值 |
|--------|------|--------|
| `spring.jpa.public.hibernate.ddl-auto` | DDL 自动生成策略 | `none`, `validate`, `update`, `create`, `create-drop` |
| `spring.jpa.public.hibernate.dialect` | 数据库方言 | 根据数据库类型选择 |
| `spring.jpa.public.hibernate.show-sql` | 是否显示 SQL | `true`, `false` |
| `spring.jpa.public.hibernate.format-sql` | 是否格式化 SQL | `true`, `false` |

### 性能优化配置

| 配置项 | 说明 | 推荐值 |
|--------|------|--------|
| `spring.jpa.public.hibernate.connection.pool-size` | 连接池大小 | `10-50` |
| `spring.jpa.public.hibernate.jdbc.batch-size` | 批处理大小 | `20-50` |
| `spring.jpa.public.hibernate.jdbc.fetch-size` | 获取大小 | `50-100` |

### 缓存配置

| 配置项 | 说明 |
|--------|------|
| `spring.jpa.public.hibernate.cache.use-second-level-cache` | 启用二级缓存 |
| `spring.jpa.public.hibernate.cache.region.factory_class` | 缓存工厂类 |
| `spring.jpa.public.hibernate.cache.use-query-cache` | 启用查询缓存 |

## 环境特定配置

### 开发环境 (application-dev.properties)

```properties
# 开发环境：显示 SQL，自动更新表结构
spring.jpa.public.hibernate.ddl-auto=update
spring.jpa.public.hibernate.show-sql=true
spring.jpa.public.hibernate.format-sql=true
spring.jpa.public.show-sql=true
```

### 测试环境 (application-test.properties)

```properties
# 测试环境：验证表结构，不显示 SQL
spring.jpa.public.hibernate.ddl-auto=validate
spring.jpa.public.hibernate.show-sql=false
spring.jpa.public.show-sql=false
```

### 生产环境 (application-prod.properties)

```properties
# 生产环境：不修改表结构，优化性能
spring.jpa.public.hibernate.ddl-auto=none
spring.jpa.public.hibernate.show-sql=false
spring.jpa.public.show-sql=false

# 性能优化
spring.jpa.public.hibernate.connection.pool-size=50
spring.jpa.public.hibernate.jdbc.batch-size=50
spring.jpa.public.hibernate.cache.use-second-level-cache=true
```

## 自定义属性支持

通过 `spring.jpa.public.properties.*` 前缀可以配置任意 Hibernate 属性：

```properties
# PostgreSQL 特定配置
spring.jpa.public.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.public.properties.hibernate.temp.use_jdbc_metadata_defaults=false

# 连接配置
spring.jpa.public.properties.hibernate.connection.characterEncoding=utf8
spring.jpa.public.properties.hibernate.connection.useUnicode=true

# 统计信息
spring.jpa.public.properties.hibernate.generate_statistics=true
spring.jpa.public.properties.hibernate.session.events.log.LOG_QUERIES_SLOWER_THAN_MS=100
```

## 配置机制优势

### 1. 避免硬编码
- 使用 Spring Boot 的 `@ConfigurationProperties` 机制
- 利用 `JpaProperties` 和 `HibernateProperties` 的标准配置
- 通过 `PublicJpaProperties` 实现类型安全的配置绑定

### 2. 配置继承和覆盖
```
全局 JPA 配置 (spring.jpa.*)
    ↓ (作为默认值)
Public 特定配置 (spring.jpa.public.*)
    ↓ (覆盖默认值)
自定义属性 (spring.jpa.public.properties.*)
```

### 3. 自动包扫描
- 使用 Reflections 自动扫描 `@EnableJpaRepositories` 注解
- 动态收集 `transactionManagerRef="publicTransactionManager"` 的包
- 无需手动维护 `setPackagesToScan` 配置

### 4. IDE 支持
- 配置属性有完整的类型检查
- 支持 IDE 的自动补全和验证
- 配置错误在编译时就能发现

## 注意事项

1. **配置优先级**：Public 特定配置 > 全局 JPA 配置 > Spring Boot 默认值
2. **环境隔离**：不同环境使用不同的配置文件
3. **密码加密**：生产环境密码建议使用 Jasypt 加密
4. **性能调优**：根据实际负载调整连接池和批处理大小
5. **DDL 策略**：生产环境建议使用 `none` 或 `validate`
6. **包扫描**：确保 `@EnableJpaRepositories` 注解的 `transactionManagerRef` 正确设置
