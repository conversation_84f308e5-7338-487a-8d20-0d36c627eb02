# 文档编写规范

## 1. 文件命名规则

- 文件名使用小写字母，单词间使用连字符（`-`）分隔，不使用空格或下划线
- 文件名应明确表示文档内容，简洁且具有描述性
- 产品名称、技术名称等专有名词应保持首字母大写，如 `Docker-部署指南.md`、`Jasypt-配置指南.md`
- 模块相关文档以模块名称开头，如 `vpn-模块设计.md`
- 所有文档使用 Markdown 格式，文件扩展名为 `.md`

## 2. 文档结构规范

- 每个文档必须包含一级标题作为文档标题
- 使用层级标题结构（`#`、`##`、`###` 等），最多不超过四级标题
- 文档开头应包含简短介绍，说明文档目的和适用范围
- 较长文档应包含目录（可使用 `[TOC]` 标记自动生成）
- 文档结尾应有相关链接或参考资料部分

## 3. 内容格式规范

### 3.1 标题格式

- 一级标题用于文档标题：`# 文档标题`
- 二级标题用于主要章节：`## 章节名称`
- 三级标题用于子章节：`### 子章节名称`
- 四级标题用于小节：`#### 小节名称`
- 标题使用中文时，应在数字与中文之间添加空格
- 标题编号可选，如使用请保持全文一致性

### 3.2 列表格式

- 无序列表使用连字符（`-`）
- 有序列表使用数字加点（`1.`）
- 列表层级缩进使用 4 个空格
- 列表项内容较长时应当换行并保持缩进对齐

### 3.3 代码块

- 代码块使用三个反引号包围，并标明语言
```java
// Java 代码示例
```

- 行内代码使用单反引号包围：`` `代码` ``
- 命令行示例应使用 `bash` 或 `shell` 作为语言标识

### 3.4 表格格式

- 表格应有表头和分隔行
- 表格列应对齐，推荐使用自动格式化工具
- 较复杂的表格可考虑使用 HTML 表格格式

### 3.5 图片和链接

- 图片引用格式：`![图片描述](图片路径)`
- 图片应存放在 `docs/images/` 目录下，按模块或功能组织
- 链接格式：`[链接文本](链接地址)`
- 项目内部链接应使用相对路径

## 4. 文档内容规范

### 4.1 通用要求

- 文档语言统一使用中文
- 专业术语和特定名词首次出现时应提供解释
- 产品名称、技术名称等专有名词应保持其正确的大小写形式，如 Docker、Spring Boot、Jasypt
- 代码、命令等技术内容使用英文原文
- 中英文混排时，英文和数字与中文之间要有空格
- 标点符号使用中文标点，代码和命令中的标点除外
- 行文风格保持客观、清晰、简洁

### 4.2 特定文档模板

#### 4.2.1 模块设计文档模板

```markdown
# [模块名称] 模块设计

## 1. 概述
[简述模块功能和目的]

## 2. 架构设计
[描述模块架构，可包含架构图]

## 3. 核心接口
[列出模块主要接口定义]

## 4. 类设计
[描述关键类的设计和关系]

## 5. 数据模型
[描述数据结构和存储方式]

## 6. 实现策略
[描述实现要点和关键算法]

## 7. 扩展性设计
[说明如何支持未来扩展]

## 8. 参考资料
[相关资料链接]
```

#### 4.2.2 工具使用指南模板

```markdown
# [工具名称] 使用指南

## 1. 简介
[简述工具用途和背景]

## 2. 安装配置
[详细安装步骤和配置说明]

## 3. 使用方法
[基本使用方法和示例]

## 4. 最佳实践
[推荐用法和注意事项]

## 5. 故障排除
[常见问题和解决方案]

## 6. 参考资料
[相关资料链接]
```

## 5. 版本控制规范

- 文档应包含最后更新日期：`_最后更新：YYYY-MM-DD_`
- 重要更新应在文档开头的更新记录部分记录
- 更新记录格式：

```markdown
## 更新记录

| 日期 | 版本 | 修改人 | 修改内容 |
|------|------|--------|----------|
| 2023-05-01 | 1.0 | 张三 | 初始版本 |
| 2023-06-15 | 1.1 | 李四 | 更新安装说明 |
```

## 6. 审阅与发布流程

- 新文档或重大更新应通过 Pull Request 提交
- 文档应经过至少一人审阅
- 技术文档应经过实际操作验证
- 发布前检查格式、链接和代码示例

## 7. 文件组织结构

```
docs/
├── README.md                     # 文档目录说明
├── 规范/                         # 规范类文档
│   └── 文档编写规范.md           # 本文档
├── 设计/                         # 设计类文档
│   ├── 整体架构设计.md
│   └── 模块设计/                 # 按模块组织的设计文档
│       └── vpn-模块设计.md
├── 指南/                         # 使用指南类文档
│   ├── Docker-部署指南.md
│   └── Jasypt-配置指南.md
└── images/                       # 文档图片资源
    ├── 公共/                     # 通用图片
    └── 模块名/                   # 按模块组织的图片
```

## 8. 示例和模板

实际文档示例请参考：
- [`docs/指南/Jasypt-配置指南.md`](../指南/Jasypt-配置指南.md)
- [`docs/指南/Docker-部署指南.md`](../指南/Docker-部署指南.md)

---

_最后更新：2025-05-29_ 