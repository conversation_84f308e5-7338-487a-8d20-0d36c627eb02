# 代码格式化规范

## 概述

本文档描述了项目中配置的 Gradle 代码格式化规则，这些规则通过 Spotless 插件实现，确保代码符合项目的编码规范。

## 格式化规则

### 1. 移除 Java 文件末尾换行符

**规则描述**：确保 Java 文件最后一个右括号 `}` 之后不包含任何换行符。

**实现方式**：
```groovy
custom 'Remove newline after final closing brace', { code ->
    return code.replaceAll('(\\})(\\s*\\n+)$', '$1')
}
```

**效果示例**：
```java
// 格式化前
public class Example {
    // 类内容
}

// 格式化后
public class Example {
    // 类内容
}
```

### 2. 替换注释中的双引号为直引号

**规则描述**：将所有 Java 注释中的双引号 `"` 替换为直引号『』，提高中文文档的可读性。

**实现方式**：
```groovy
custom 'Replace quotes in comments', { code ->
    // 处理单行注释 //
    code = code.replaceAll('(//.*?)"([^"]*?)"', '$1『$2』')
    
    // 处理多行注释 /* */
    code = code.replaceAll('(/\\*[\\s\\S]*?\\*/)', { match ->
        return match[0].replaceAll('"([^"]*?)"', '『$1』')
    })
    
    // 处理 Javadoc 注释 /** */
    code = code.replaceAll('(/\\*\\*[\\s\\S]*?\\*/)', { match ->
        return match[0].replaceAll('"([^"]*?)"', '『$1』')
    })
    
    return code
}
```

**效果示例**：
```java
// 格式化前
/**
 * 处理"用户输入"的方法
 */
// 验证"参数有效性"

// 格式化后
/**
 * 处理『用户输入』的方法
 */
// 验证『参数有效性』
```

### 3. 替换 Javadoc 中的 `<strong>` 标签为 `<b>` 标签

**规则描述**：自动将 Javadoc 注释中的 `<strong>` 标签替换为更简短的 `<b>` 标签，减少代码长度。

**实现方式**：
```groovy
custom 'Replace strong tags with b tags in Javadoc', { code ->
    code = code.replaceAll('(/\\*\\*[\\s\\S]*?\\*/)', { match ->
        String javadoc = match[0]
        javadoc = javadoc.replaceAll('<strong>', '<b>')
        javadoc = javadoc.replaceAll('</strong>', '</b>')
        return javadoc
    })
    
    return code
}
```

**效果示例**：
```java
// 格式化前
/**
 * <ul>
 *   <li><strong>特性一</strong>：描述内容</li>
 *   <li><strong>特性二</strong>：描述内容</li>
 * </ul>
 */

// 格式化后
/**
 * <ul>
 *   <li><b>特性一</b>：描述内容</li>
 *   <li><b>特性二</b>：描述内容</li>
 * </ul>
 */
```

### 4. 修正 Javadoc 中 `<li></li>` 标签句号位置

**规则描述**：只处理明确错误的情况，即句号在 `</li>` 标签外的情况，将句号移到标签内部。

**实现方式**：
```groovy
custom 'Fix Javadoc li tags period - minimal', { code ->
    // 只处理明确错误的情况：句号在 </li> 标签外
    code = code.replaceAll('<li>([^<]*?)</li>。', '<li>$1。</li>')
    
    return code
}
```

**效果示例**：
```java
// 格式化前（句号在标签外）
/**
 * <ul>
 *   <li>支持参数验证</li>。
 * </ul>
 */

// 格式化后（句号移到标签内）
/**
 * <ul>
 *   <li>支持参数验证。</li>
 * </ul>
 */
```

## 使用方法

### 运行格式化命令

```bash
# 应用所有格式化规则
./gradlew format

# 或者只运行 Spotless 格式化
./gradlew spotlessApply

# 检查格式化状态（不修改文件）
./gradlew spotlessCheck
```

### 验证格式化效果

格式化完成后，可以通过以下方式验证：

1. **检查文件末尾**：确保 `.java` 文件最后的 `}` 后没有多余换行
2. **检查注释**：确保双引号已替换为直引号『』
3. **检查 HTML 标签**：确保 `<strong>` 已替换为 `<b>`
4. **检查句号位置**：确保 `<li></li>` 标签内的句号位置正确

## 设计原则

### 1. 保守原则
- 只进行必要的格式化，不过度修改代码
- 避免添加多余的标点符号
- 保持原有的代码风格和语义

### 2. 一致性原则
- 统一代码格式标准
- 确保团队成员使用相同的格式规范
- 减少代码审查中的格式争议

### 3. 可读性原则
- 提高中文文档的可读性
- 使用更简洁的 HTML 标签
- 保持代码的整洁性

## 配置文件位置

格式化规则配置在项目根目录的 `build.gradle` 文件中的 `spotless` 块内：

```groovy
spotless {
    java {
        // 基础格式化配置
        googleJavaFormat()
        removeUnusedImports()
        formatAnnotations()
        
        // 自定义格式化规则
        custom 'Remove newline after final closing brace', { /* 规则实现 */ }
        custom 'Replace quotes in comments', { /* 规则实现 */ }
        custom 'Replace strong tags with b tags in Javadoc', { /* 规则实现 */ }
        custom 'Fix Javadoc li tags period - minimal', { /* 规则实现 */ }
    }
}
```

## 注意事项

### 1. 备份重要文件
首次运行格式化前建议备份重要文件或确保代码已提交到版本控制系统。

### 2. 逐步应用
可以先在测试文件上验证格式化效果，确认无误后再应用到整个项目。

### 3. 团队协作
确保团队成员都了解这些格式化规则，并在开发过程中定期运行格式化命令。

### 4. 持续集成
建议在 CI/CD 流程中集成格式化检查，确保提交的代码符合格式规范。

## 扩展配置

如需修改或添加新的格式化规则，请编辑 `build.gradle` 文件中的相应配置，并更新本文档。

### 添加新规则的步骤

1. 在 `build.gradle` 的 `spotless.java` 块中添加新的 `custom` 规则
2. 编写正则表达式或字符串替换逻辑
3. 在测试文件上验证规则效果
4. 更新本文档，添加规则说明和示例
5. 通知团队成员新的格式化规则
