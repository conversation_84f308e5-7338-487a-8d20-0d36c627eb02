# 项目文档

本目录包含 nanfeng-tools 项目的所有文档。文档按照类型组织，便于查找和维护。

## 文档目录结构

```
docs/
├── README.md                     # 文档目录说明
├── 规范/                         # 规范类文档
│   ├── 文档编写规范.md           # 文档编写规范
│   ├── 代码格式化规范.md         # 代码格式化规则说明
│   └── HTTP客户端API规范.md      # HTTP 客户端 API 设计规范
├── 设计/                         # 设计类文档
│   ├── 整体架构设计.md
│   └── 模块设计/                 # 按模块组织的设计文档
│       ├── vpn-server-模块设计.md
│       └── HTTP客户端模块设计.md  # HTTP 客户端模块架构设计
├── 指南/                         # 使用指南类文档
│   ├── Docker-部署指南.md
│   ├── Jasypt-配置指南.md
│   └── HTTP客户端使用指南.md     # HTTP 客户端详细使用说明
└── images/                       # 文档图片资源
    ├── 公共/                     # 通用图片
    └── 模块名/                   # 按模块组织的图片
```

## 文档编写规范

所有文档应遵循 [文档编写规范](./规范/文档编写规范.md)，确保统一的格式和风格。

## 文档分类

### 规范文档

- [文档编写规范](./规范/文档编写规范.md) - 项目文档的编写标准和规范
- [代码格式化规范](./规范/代码格式化规范.md) - Gradle 代码格式化规则说明
- [HTTP客户端API规范](./规范/HTTP客户端API规范.md) - HTTP 客户端 API 设计规范

### 设计文档

- [VPN Server 模块设计](./设计/模块设计/vpn-server-模块设计.md) - VPN 模块下服务器管理子模块的设计方案
- [HTTP客户端模块设计](./设计/模块设计/HTTP客户端模块设计.md) - HTTP 客户端模块架构设计

### 使用指南

- [Docker 部署指南](./指南/Docker-部署指南.md) - Docker 环境下的部署说明
- [Jasypt 配置指南](./指南/Jasypt-配置指南.md) - Jasypt 配置加密工具的使用说明
- [HTTP客户端使用指南](指南/HTTP%20客户端使用指南.md) - HTTP 客户端模块的详细使用说明

---

_最后更新：2025-06-19_