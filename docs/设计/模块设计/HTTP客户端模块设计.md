# HTTP 客户端模块设计

## 概述

HTTP 客户端模块（`cn.bluesking.nanfeng.tools.common.http`）是一个基于 Reactor 的响应式 HTTP 客户端，提供流畅的链式 API 和强大的功能特性。

## 核心特性

- **响应式编程**：基于 Project Reactor，返回 `Mono` 类型
- **链式 API**：提供流畅的链式调用接口
- **代理支持**：支持 HTTP 和 SOCKS 代理，包括认证
- **灵活配置**：支持请求头、超时、重试等配置
- **多种格式**：支持 JSON、表单、文本等多种请求和响应格式
- **错误处理**：提供详细的错误信息和异常处理

## 模块架构

### 核心接口

#### ApiClient
主要的 HTTP 客户端接口，提供请求构建的入口点。

```java
public interface ApiClient {
    RequestBuilder request();
    
    enum ProxyType { HTTP, SOCKS }
    
    interface RequestBuilder { /* 请求构建器 */ }
}
```

#### ResponseParser
响应解析器接口，负责将 HTTP 响应体解析为指定类型的对象。

```java
public interface ResponseParser<T> {
    T parse(String responseBody);
    default Exception parseError(String responseBody) { return null; }
    default String getRequestId() { return null; }
}
```

### 核心实现类

#### OkApiClient
基于 OkHttp 的 `ApiClient` 实现，提供完整的 HTTP 客户端功能。

**主要特性：**
- 支持所有标准 HTTP 方法（GET、POST、PUT、DELETE、PATCH、HEAD、OPTIONS）
- 代理配置支持（HTTP/SOCKS，带认证）
- 请求和响应日志记录
- SSL 配置和证书验证
- 连接池管理

#### GenericResponseParser
通用响应解析器，支持多种解析方式：
- JSON 反序列化（使用 Jackson）
- 自定义解析函数
- 字符串直接返回

#### ApiClientFactory
客户端工厂类，提供便捷的客户端创建方法。

### 配置类

#### ApiClientConfig
客户端配置类，包含：
- 基础 URL 配置
- 超时设置
- 日志开关
- SSL 配置
- 默认请求头

#### HttpProxyConfig
代理配置类，支持：
- HTTP/SOCKS 代理类型
- 代理服务器地址和端口
- 认证信息
- 代理开关

### 枚举类

#### ContentType
HTTP 内容类型枚举，定义常用的 Content-Type 值：
- JSON (`application/json`)
- 表单数据 (`application/x-www-form-urlencoded`)
- 文本 (`text/plain`)
- HTML (`text/html`)
- XML (`application/xml`)
- 多部分表单 (`multipart/form-data`)

## 使用模式

### 基本使用

```java
// 创建客户端
ApiClient client = ApiClientFactory.create();

// 简单 GET 请求
Mono<User[]> users = client.request()
    .uri("/api/users")
    .get()
    .retrieveJson(User[].class);
```

### 代理配置

```java
// HTTP 代理
client.request()
    .uri("/api/data")
    .proxy("proxy.company.com", 8080)
    .get()
    .retrieveJson(String.class);

// 带认证的代理
client.request()
    .uri("/api/secure")
    .proxy("proxy.company.com", 8080, "username", "password")
    .get()
    .retrieveJson(String.class);

// SOCKS 代理
client.request()
    .uri("/api/data")
    .proxy("socks-proxy.company.com", 1080)
    .proxyType(ProxyType.SOCKS)
    .get()
    .retrieveJson(String.class);
```

### 请求体配置

```java
// JSON 请求体
client.request()
    .uri("/api/users")
    .post()
    .bodyJson(newUser)
    .retrieveJson(User.class);

// 表单数据
client.request()
    .uri("/api/login")
    .post()
    .formData(params -> {
        params.put("username", "admin");
        params.put("password", "secret");
    })
    .retrieveJson(LoginResponse.class);
```

## 设计原则

### 1. 响应式优先
所有 API 都返回 `Mono` 类型，支持响应式编程模式。

### 2. 链式调用
提供流畅的链式 API，提高代码可读性。

### 3. 配置灵活
支持全局配置和请求级别配置，满足不同场景需求。

### 4. 错误处理
提供详细的错误信息和异常处理机制。

### 5. 扩展性
通过接口设计支持自定义实现和扩展。

## 技术依赖

- **OkHttp**：底层 HTTP 客户端实现
- **Project Reactor**：响应式编程支持
- **Jackson**：JSON 序列化和反序列化
- **SLF4J**：日志记录

## 性能特性

- **连接池**：复用 HTTP 连接，提高性能
- **异步处理**：基于响应式编程，支持高并发
- **内存优化**：流式处理大文件，避免内存溢出
- **缓存支持**：支持 HTTP 缓存机制

## 安全特性

- **SSL/TLS**：支持 HTTPS 和自定义 SSL 配置
- **代理认证**：支持代理服务器认证
- **请求签名**：支持自定义请求签名
- **敏感信息保护**：可禁用敏感请求的日志记录
