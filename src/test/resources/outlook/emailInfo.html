<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>炮哥生成邮件工具</title>
    <script src="/assets/js/lottie.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
    <div class="max-w-4xl mx-auto px-4 py-8">
        <div class="bg-white rounded-xl shadow-xl p-8 backdrop-blur-sm bg-opacity-90">
            <div class="flex items-center justify-center mb-8">
                <div id="lottie-animation" class="w-24 h-24"></div>
                <h1 class="text-3xl font-bold text-gray-800 ml-4">炮哥生成邮件工具</h1>
            </div>

            <form method="POST" action="http://127.0.0.1:8080/emailInfo/generate" enctype="multipart/form-data" class="space-y-6">
                <input type="hidden" name="MAX_FILE_SIZE" value="10000000">
                
                <div class="space-y-4">
                    <div class="form-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">选择收件人信息 Excel 文件</label>
                        <input type="file" accept=".xls,.xlsx" name="recipientInfosFile" 
                            class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary/90 transition-colors duration-200" />
                    </div>

                    <div class="form-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">选择 MSG 模板文件</label>
                        <input type="file" accept=".msg,.eml,.emltpl" name="msgTemplateFile" 
                            class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary/90 transition-colors duration-200" />
                    </div>

                    <div class="form-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">邮件主题</label>
                        <input type="text" name="subjectTemplate" 
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary transition-colors duration-200" />
                    </div>

                    <div class="form-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">邮件内容</label>
                        <textarea rows="10" name="contentTemplate" 
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary transition-colors duration-200"></textarea>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <input type="checkbox" name="isOneEmailPerRecipient" value="true" 
                                class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded transition-colors duration-200" />
                            <label class="ml-2 block text-sm text-gray-700">按每个收件人生成一封邮件</label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" id="isExportAsEml" name="isExportAsEml" value="true" 
                                class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded transition-colors duration-200" />
                            <label class="ml-2 block text-sm text-gray-700">导出 EML 格式（MacOS 系统）</label>
                        </div>
                    </div>
                </div>

                <div class="flex justify-center">
                    <button type="submit" 
                        class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200">
                        生成邮件
                    </button>
                </div>
            </form>

            <div class="mt-8 space-y-6">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h2 class="text-lg font-semibold text-gray-800 mb-3">邮件内容中支持的占位符</h2>
                    <ul class="space-y-2 text-sm text-gray-600">
                        <li class="flex items-start">
                            <span class="text-primary mr-2">•</span>
                            <span><code class="bg-gray-100 px-2 py-1 rounded">{{hi}}</code>：效果『Hi Tom, Jerry and Jack』，效果上等价于『Hi {{recipientFirstNames}}』。</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-primary mr-2">•</span>
                            <span><code class="bg-gray-100 px-2 py-1 rounded">{{hey}}</code>：效果『Hey Tom, Jerry and Jack』，效果上等价于『Hey {{recipientFirstNames}}』。</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-primary mr-2">•</span>
                            <span><code class="bg-gray-100 px-2 py-1 rounded">{{recipientFirstNames}}</code>：收件人的 first name，单个收件人时候效果『Tom』，多个收件人时候效果『Tom, Jerry and Jack』。</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-primary mr-2">•</span>
                            <span><code class="bg-gray-100 px-2 py-1 rounded">{{content}}</code>：网页上填写的『邮件内容』。</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-primary mr-2">•</span>
                            <span><code class="bg-gray-100 px-2 py-1 rounded">{{subject}}</code>：网页上填写的『邮件主题』。</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-primary mr-2">•</span>
                            <span><code class="bg-gray-100 px-2 py-1 rounded">{{companyName}}</code>：Excel 文件中读取到的『公司名称』。</span>
                        </li>
                    </ul>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <h2 class="text-lg font-semibold text-gray-800 mb-3">Excel 文件格式识别规则</h2>
                    <ul class="space-y-2 text-sm text-gray-600">
                        <li class="flex items-start">
                            <span class="text-primary mr-2">•</span>
                            <span><code class="bg-gray-100 px-2 py-1 rounded">SheetName</code>：每一个 Sheet 会生成一个同名的压缩包。</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-primary mr-2">•</span>
                            <span><code class="bg-gray-100 px-2 py-1 rounded">公司</code>：第一行列名称是『公司』的列会被识别成公司字段，用于生成 {{companyName}}。</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-primary mr-2">•</span>
                            <span><code class="bg-gray-100 px-2 py-1 rounded">联系人</code>：第一行列名称是『联系人』的列会被识别成联系人/收件人字段（该列第一行是合并单元格）。</span>
                            <ul class="ml-4 mt-2 space-y-2">
                                <li class="flex items-start">
                                    <span class="text-primary mr-2">•</span>
                                    <span>联系人单元格第一行会被识别成收件人名字，用于生成 {{hi}}、{{hey}}、{{recipientFirstNames}}。</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-primary mr-2">•</span>
                                    <span>联系人单元格中的邮箱地址需要单独一行，会被识别成收件人邮箱地址，作为生成邮件的收件人地址。</span>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Lottie 动画
        const animation = lottie.loadAnimation({
            container: document.getElementById('lottie-animation'),
            renderer: 'svg',
            loop: true,
            autoplay: true,
            path: 'https://assets5.lottiefiles.com/packages/lf20_2cwDXD.json'
        });

        // 检测用户操作系统是否是 macOS
        if (navigator.userAgent.indexOf('Mac OS X') !== -1) {
            document.getElementById('isExportAsEml').checked = true;
        }

        // 添加文件拖放功能
        const fileInputs = document.querySelectorAll('input[type="file"]');
        fileInputs.forEach(input => {
            input.addEventListener('dragover', (e) => {
                e.preventDefault();
                input.parentElement.classList.add('border-primary');
            });

            input.addEventListener('dragleave', (e) => {
                e.preventDefault();
                input.parentElement.classList.remove('border-primary');
            });

            input.addEventListener('drop', (e) => {
                e.preventDefault();
                input.parentElement.classList.remove('border-primary');
                input.files = e.dataTransfer.files;
            });
        });
    </script>
</body>
</html>