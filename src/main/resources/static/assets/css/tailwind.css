@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --primary: #3B82F6;
        --secondary: #10B981;
    }
}

@layer components {
    .btn-primary {
        @apply inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary;
    }
    
    .form-input {
        @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary;
    }
    
    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-2;
    }
    
    .file-input {
        @apply block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary/90;
    }
    
    .card {
        @apply bg-white rounded-lg shadow-lg p-8;
    }
    
    .info-card {
        @apply bg-gray-50 p-4 rounded-lg;
    }
    
    .info-title {
        @apply text-lg font-semibold text-gray-800 mb-3;
    }
    
    .info-list {
        @apply space-y-2 text-sm text-gray-600;
    }
    
    .info-item {
        @apply flex items-start;
    }
    
    .info-bullet {
        @apply text-primary mr-2;
    }
    
    .code-block {
        @apply bg-gray-100 px-2 py-1 rounded;
    }
} 