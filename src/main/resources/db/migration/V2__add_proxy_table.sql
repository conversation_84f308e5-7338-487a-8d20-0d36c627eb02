-- V2__add_proxy_table.sql
-- 添加代理服务表结构
-- 作者: 随心
-- 日期: 2025-07-02

-- 创建代理信息序列
CREATE SEQUENCE IF NOT EXISTS proxy_info_sequence
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

-- 创建代理信息表
CREATE TABLE IF NOT EXISTS proxy_info (
    id BIGINT PRIMARY KEY DEFAULT nextval('proxy_info_sequence'),
    proxy_type INT2 NOT NULL,
    local_port INTEGER NOT NULL,
    remote_port INTEGER NOT NULL,
    remote_host VARCHAR(100) NOT NULL,
    description VARCHAR(200),
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    server_info_id BIGINT NOT NULL,
    CONSTRAINT fk_proxy_server_info FOREIGN KEY (server_info_id) REFERENCES server_info(id) ON DELETE CASCADE
);

-- 添加表和列注释
COMMENT ON TABLE proxy_info IS '代理信息表';
COMMENT ON COLUMN proxy_info.id IS '代理 ID';
COMMENT ON COLUMN proxy_info.proxy_type IS '代理类型';
COMMENT ON COLUMN proxy_info.local_port IS '本地端口';
COMMENT ON COLUMN proxy_info.remote_port IS '远程端口';
COMMENT ON COLUMN proxy_info.remote_host IS '远程主机';
COMMENT ON COLUMN proxy_info.description IS '描述';
COMMENT ON COLUMN proxy_info.enabled IS '是否启用';
COMMENT ON COLUMN proxy_info.created_at IS '创建时间';
COMMENT ON COLUMN proxy_info.server_info_id IS '服务器信息 ID';

-- 创建索引
CREATE INDEX idx_proxy_server_info_id ON proxy_info(server_info_id);
CREATE INDEX idx_proxy_type ON proxy_info(proxy_type);
CREATE INDEX idx_proxy_enabled ON proxy_info(enabled); 