-- V1__add_server_info_table.sql
-- 初始化数据库结构
-- 作者: 随心
-- 日期: 2025-07-02

-- 创建序列
CREATE SEQUENCE IF NOT EXISTS server_info_sequence
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

-- 创建服务器信息表
CREATE TABLE IF NOT EXISTS server_info (
    id BIGINT PRIMARY KEY DEFAULT nextval('server_info_sequence'),
    name VARCHAR(100) NOT NULL,
    host VARCHAR(100) NOT NULL,
    port INTEGER NOT NULL DEFAULT 22,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(100),
    private_key TEXT,
    status INT2 NOT NULL,
    last_check_time TIMESTAMP,
    os_type INT2,
    os_version VARCHAR(100),
    cpu_info VARCHAR(200),
    memory_info VARCHAR(200),
    disk_info VARCHAR(200),
    auto_reconnect BOOLEAN DEFAULT TRUE,
    connection_timeout INTEGER DEFAULT 30000
);

-- 添加表和列注释
COMMENT ON TABLE server_info IS '服务器信息表';
COMMENT ON COLUMN server_info.id IS '服务器 ID';
COMMENT ON COLUMN server_info.name IS '服务器名称';
COMMENT ON COLUMN server_info.host IS '主机地址';
COMMENT ON COLUMN server_info.port IS 'SSH 端口';
COMMENT ON COLUMN server_info.username IS '用户名';
COMMENT ON COLUMN server_info.password IS '密码';
COMMENT ON COLUMN server_info.private_key IS '私钥';
COMMENT ON COLUMN server_info.status IS '服务器状态';
COMMENT ON COLUMN server_info.last_check_time IS '最后检查时间';
COMMENT ON COLUMN server_info.os_type IS '操作系统类型';
COMMENT ON COLUMN server_info.os_version IS '操作系统版本';
COMMENT ON COLUMN server_info.cpu_info IS 'CPU 信息';
COMMENT ON COLUMN server_info.memory_info IS '内存信息';
COMMENT ON COLUMN server_info.disk_info IS '磁盘信息';
COMMENT ON COLUMN server_info.auto_reconnect IS '是否自动重连';
COMMENT ON COLUMN server_info.connection_timeout IS '连接超时时间（毫秒）';

-- 创建索引
CREATE INDEX idx_server_info_name ON server_info(name);
CREATE INDEX idx_server_info_host ON server_info(host);
CREATE INDEX idx_server_info_status ON server_info(status); 