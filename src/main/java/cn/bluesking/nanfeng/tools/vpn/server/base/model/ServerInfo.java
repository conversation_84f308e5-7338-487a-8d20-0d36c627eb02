package cn.bluesking.nanfeng.tools.vpn.server.base.model;

import cn.bluesking.nanfeng.tools.vpn.server.proxy.model.ProxyInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 服务器信息实体类。
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
@Entity
public class ServerInfo {

    /**
     * 服务器 ID。
     */
    @Id
    @SequenceGenerator(
        name = "server_info_sequence",
        sequenceName = "server_info_sequence",
        allocationSize = 1
    )
    @GeneratedValue(
        strategy = GenerationType.SEQUENCE,
        generator = "server_info_sequence"
    )
    private Long id;

    /**
     * 服务器名称。
     */
    @Column(nullable = false)
    private String name;

    /**
     * 主机地址。
     */
    @Column(nullable = false)
    private String host;

    /**
     * 端口。
     */
    @Column(nullable = false)
    private Integer port;

    /**
     * 用户名。
     */
    @Column(nullable = false)
    private String username;

    /**
     * 密码。
     */
    @JsonIgnore
    @Column
    private String password;

    /**
     * 私钥。
     */
    @JsonIgnore
    @Lob
    @Column(columnDefinition = "text")
    private String privateKey;

    /**
     * 服务器状态。
     */
    @Column(nullable = false, columnDefinition = "int2")
    @Convert(converter = ServerStatus.Converter.class)
    private ServerStatus status;

    /**
     * 最后检查时间。
     */
    @Column
    private LocalDateTime lastCheckTime;

    /**
     * 操作系统类型。
     */
    @Column(columnDefinition = "int2")
    @Convert(converter = OsType.Converter.class)
    private OsType osType;

    /**
     * 操作系统版本。
     */
    @Column
    private String osVersion;

    /**
     * CPU 信息。
     */
    @Column
    private String cpuInfo;

    /**
     * 内存信息。
     */
    @Column
    private String memoryInfo;

    /**
     * 磁盘信息。
     */
    @Column
    private String diskInfo;

    /**
     * 是否自动重连。
     */
    @Column
    private Boolean autoReconnect;

    /**
     * 连接超时时间（毫秒）。
     */
    @Column
    private Integer connectionTimeout;

    /**
     * 代理列表。
     */
    @OneToMany(
        cascade = CascadeType.ALL,
        orphanRemoval = true,
        mappedBy = "serverInfo",
        fetch = FetchType.LAZY
    )
    private List<ProxyInfo> proxies;

    /*-------------------- constructor --------------------*/

    /**
     * 默认构造函数。
     */
    public ServerInfo() {
        this.port = 22;
        this.status = ServerStatus.UNKNOWN;
        this.lastCheckTime = LocalDateTime.now();
        this.autoReconnect = true;
        this.connectionTimeout = 30000;
    }

    /**
     * 带参数构造函数。
     *
     * @param name 服务器名称
     * @param host 主机地址
     * @param port SSH 端口
     * @param username 用户名
     */
    public ServerInfo(String name, String host, Integer port, String username) {
        this();
        this.name = name;
        this.host = host;
        this.port = port;
        this.username = username;
    }

    /*-------------------- getter --------------------*/

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getHost() {
        return host;
    }

    public Integer getPort() {
        return port;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public ServerStatus getStatus() {
        return status;
    }

    public LocalDateTime getLastCheckTime() {
        return lastCheckTime;
    }

    public OsType getOsType() {
        return osType;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public String getCpuInfo() {
        return cpuInfo;
    }

    public String getMemoryInfo() {
        return memoryInfo;
    }

    public String getDiskInfo() {
        return diskInfo;
    }

    public boolean isAutoReconnect() {
        return autoReconnect;
    }

    public Integer getConnectionTimeout() {
        return connectionTimeout;
    }

    public List<ProxyInfo> getProxies() {
        return proxies;
    }

    /*-------------------- setter --------------------*/

    public void setId(Long id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public void setStatus(ServerStatus status) {
        this.status = status;
    }

    public void setLastCheckTime(LocalDateTime lastCheckTime) {
        this.lastCheckTime = lastCheckTime;
    }

    public void setOsType(OsType osType) {
        this.osType = osType;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public void setCpuInfo(String cpuInfo) {
        this.cpuInfo = cpuInfo;
    }

    public void setMemoryInfo(String memoryInfo) {
        this.memoryInfo = memoryInfo;
    }

    public void setDiskInfo(String diskInfo) {
        this.diskInfo = diskInfo;
    }

    public void setAutoReconnect(Boolean autoReconnect) {
        this.autoReconnect = autoReconnect;
    }

    public void setConnectionTimeout(Integer connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }

    public void setProxies(List<ProxyInfo> proxies) {
        this.proxies = proxies;
    }

    /*-------------------- equals and hashCode --------------------*/

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ServerInfo that = (ServerInfo) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "{\"ServerInfo\":{" +
                   "\"id\": \"" + id + '\"' +
                   ", \"name\": \"" + name + '\"' +
                   ", \"host\": \"" + host + '\"' +
                   ", \"port\": " + port +
                   ", \"username\": \"" + username + '\"' +
                   ", \"password\": \"" + password + '\"' +
                   ", \"privateKey\": \"" + privateKey + '\"' +
                   ", \"status\": \"" + status + '\"' +
                   ", \"lastCheckTime\": \"" + lastCheckTime + '\"' +
                   ", \"osType\": \"" + osType + '\"' +
                   ", \"osVersion\": \"" + osVersion + '\"' +
                   ", \"cpuInfo\": \"" + cpuInfo + '\"' +
                   ", \"memoryInfo\": \"" + memoryInfo + '\"' +
                   ", \"diskInfo\": \"" + diskInfo + '\"' +
                   ", \"autoReconnect\": " + autoReconnect +
                   ", \"connectionTimeout\": " + connectionTimeout +
                   ", \"proxies\": " + proxies +
                   "}}";
    }

}