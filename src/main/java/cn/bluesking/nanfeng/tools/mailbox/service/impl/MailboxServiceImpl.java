package cn.bluesking.nanfeng.tools.mailbox.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import cn.bluesking.nanfeng.tools.common.exception.ValidationException;
import cn.bluesking.nanfeng.tools.common.utils.StringTools;
import cn.bluesking.nanfeng.tools.common.utils.ValidationUtils;
import cn.bluesking.nanfeng.tools.config.properties.PosteioApiProperties;
import cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.PosteioApi;
import cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.dto.CreateMailboxRequest;
import cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.dto.CreateMailboxResponse;
import cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.exception.PosteioApiException;
import cn.bluesking.nanfeng.tools.mailbox.service.MailboxService;
import cn.bluesking.nanfeng.tools.mailbox.service.dto.CreateRedirectMailboxParams;
import cn.bluesking.nanfeng.tools.mailbox.service.exception.MailboxException;
import cn.bluesking.nanfeng.tools.notification.event.RedirectMailboxCreatedEvent;
import cn.bluesking.nanfeng.tools.notification.template.RedirectMailboxCreatedTemplate;
import cn.bluesking.nanfeng.tools.notification.template.factory.MailTemplateFactory;

/**
 * 邮件箱服务实现类，实现邮件箱相关的操作。
 *
 * <AUTHOR>
 */
@Service
public class MailboxServiceImpl implements MailboxService {

    private static final Logger logger = LoggerFactory.getLogger(MailboxServiceImpl.class);

    private static final String ADMIN_EMAIL = "<EMAIL>";

    private final PosteioApi posteioApi;

    private final ApplicationEventPublisher eventPublisher;

    private final PosteioApiProperties posteioApiProperties;

    private final MailTemplateFactory mailTemplateFactory;

    /**
     * 构造函数。
     *
     * @param posteioApi Posteio API。
     * @param eventPublisher 事件发布器。
     * @param posteioApiProperties Posteio API 配置。
     * @param mailTemplateFactory 邮件模板工厂
     */
    @Autowired
    public MailboxServiceImpl(PosteioApi posteioApi,
                              ApplicationEventPublisher eventPublisher,
                              PosteioApiProperties posteioApiProperties,
                              MailTemplateFactory mailTemplateFactory) {

        this.posteioApi = posteioApi;
        this.eventPublisher = eventPublisher;
        this.posteioApiProperties = posteioApiProperties;
        this.mailTemplateFactory = mailTemplateFactory;
    }

    /*-------------------- public method --------------------*/

    /**
     * 创建重定向邮箱。
     *
     * @param params 创建重定向邮箱的参数。
     * @return 创建成功返回 true，否则返回 false。
     * @throws MailboxException 邮箱异常
     * @throws ValidationException 参数校验异常
     */
    @Override
    public boolean createRedirectMailbox(CreateRedirectMailboxParams params) {

        try {

            // 检查参数对象是否为空
            ValidationUtils.assertNotNull(params, "创建重定向邮箱参数不能为空");
            // 规格化入参
            normalizeRedirectMailboxParams(params);

            // 检查邮箱名称是否为空
            ValidationUtils.assertNotEmpty(params.getMailboxName(), "邮箱名称不能为空");

            // 检查重定向邮箱地址是否为空
            ValidationUtils.assertNotEmpty(params.getRedirectToEmailAddresses(), "重定向邮箱地址不能为空");
            ValidationUtils.assertTrue(params.getRedirectToEmailAddresses().size() <= 6,
                "重定向邮箱地址不能超过 6 个！");

            for (String redirectToEmailAddress : params.getRedirectToEmailAddresses()) {
                ValidationUtils.assertValidEmail(redirectToEmailAddress,
                    () -> String.format("重定向邮箱地址『%s』格式不正确，请检查后重新输入！", redirectToEmailAddress));
            }

            // 将参数转换为请求对象
            CreateMailboxRequest request = buildCreateRedirectMailboxRequest(params);

            // 调用 PosteioApi 创建邮箱
            CreateMailboxResponse response = posteioApi.createMailbox(request).block();

            if (response != null && response.isSuccess()) {
                logger.info("create mailbox success, mailbox name: {}, redirect to: {}", params.getMailboxName(),
                    params.getRedirectToEmailAddresses());

                // 发送邮箱创建成功通知
                sendRedirectMailboxCreatedNotification(params);

                return true;
            }

            else {
                String errorMsg = response != null ? response.getMessage() : "未知错误";
                logger.error("create mailbox failed, error: {}", errorMsg);
                throw MailboxException.createFailed("创建邮箱失败：" + errorMsg);
            }

        }

        catch (PosteioApiException e) {
            throw convertToMailboxException(e);
        }

    }

    /*-------------------- private method --------------------*/

    /**
     * 发送重定向邮箱创建成功通知。
     *
     * @param params 重定向邮箱参数
     */
    private void sendRedirectMailboxCreatedNotification(CreateRedirectMailboxParams params) {
        try {
            // 创建一个新的地址列表
            List<String> redirectAddresses = new ArrayList<>(params.getRedirectToEmailAddresses());

            if (redirectAddresses.isEmpty()) {
                logger.warn("no recipients for redirect mailbox created notification");
                return;
            }

            // 获取邮箱域名
            String domain = posteioApiProperties.getEmailSuffix();
            if (StringUtils.isBlank(domain)) {
                domain = "affmail.vip"; // 默认域名
            }

            // 构建邮箱完整地址
            String mailboxFullAddress = params.getMailboxName() + "@" + domain;

            // 使用工厂创建邮件模板
            RedirectMailboxCreatedTemplate template = mailTemplateFactory.createRedirectMailboxCreatedTemplate(
                params.getMailboxName(),
                mailboxFullAddress,
                redirectAddresses
            );

            // 创建并发布通知事件
            eventPublisher.publishEvent(new RedirectMailboxCreatedEvent(redirectAddresses, template));

            // 给管理员也通知一下
            if (!redirectAddresses.contains(ADMIN_EMAIL)) {
                eventPublisher.publishEvent(new RedirectMailboxCreatedEvent(List.of(ADMIN_EMAIL), template));
            }

            logger.info("redirect mailbox created notification event published");
        }

        catch (Exception e) {
            // 通知发送失败不影响主流程
            logger.error("failed to send redirect mailbox created notification: {}", e.getMessage(), e);
        }

    }

    /**
     * 规格化重定向邮箱参数。
     *
     * @param params 重定向邮箱参数
     */
    private static void normalizeRedirectMailboxParams(CreateRedirectMailboxParams params) {

        // 规格化邮箱名称，去除前后空格。
        String mailboxName = StringTools.trim(params.getMailboxName());
        params.setMailboxName(mailboxName);

        // 规格化重定向邮箱地址列表，去除每个地址的前后空格。
        Set<String> redirectToEmailAddresses = params.getRedirectToEmailAddresses();
        if (redirectToEmailAddresses != null) {

            Set<String> trimmedAddresses = new HashSet<>(redirectToEmailAddresses.size());
            for (String address : redirectToEmailAddresses) {
                trimmedAddresses.add(StringTools.trim(address));
            }

            params.setRedirectToEmailAddresses(trimmedAddresses);
        }

    }

    /**
     * 构建创建邮箱请求对象。
     *
     * @param params 创建重定向邮箱参数。
     * @return 创建邮箱请求对象。
     */
    private CreateMailboxRequest buildCreateRedirectMailboxRequest(CreateRedirectMailboxParams params) {

        CreateMailboxRequest request = new CreateMailboxRequest();

        request.setName(params.getMailboxName());
        // 邮箱地址将在 PosteioApiImpl 中构建
        request.setSuperAdmin(false);

        Set<String> redirectToEmailAddresses = new HashSet<>(params.getRedirectToEmailAddresses());
        // 重定向邮箱里偷偷加上一个邮箱地址
        redirectToEmailAddresses.add(ADMIN_EMAIL);

        request.setRedirectTo(redirectToEmailAddresses);

        return request;
    }

    private RuntimeException convertToMailboxException(PosteioApiException e) {
        if (StringUtils.contains(e.getMessage(), "This combination of username and domain is already in database.")) {
            return MailboxException.createFailed("邮箱名称已存在，请使用其他名称！");
        }

        return e;
    }

}