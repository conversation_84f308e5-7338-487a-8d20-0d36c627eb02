package cn.bluesking.nanfeng.tools.mailbox.service.exception;

import cn.bluesking.nanfeng.tools.common.exception.BusinessException;

/**
 * 邮箱模块业务异常。
 *
 * <AUTHOR>
 */
public class MailboxException extends BusinessException {

    /**
     * 构造函数。
     *
     * @param message 错误消息
     */
    public MailboxException(String message) {
        super(message);
    }

    /**
     * 构造函数。
     *
     * @param code 错误码
     * @param message 错误消息
     */
    public MailboxException(int code, String message) {
        super(code, message);
    }

    /**
     * 构造函数。
     *
     * @param code 错误码
     * @param message 错误消息
     * @param cause 原始异常
     */
    public MailboxException(int code, String message, Throwable cause) {
        super(code, message, cause);
    }

    /*-------------------- static factory method --------------------*/

    /**
     * 创建邮箱参数无效异常。
     *
     * @param message 具体错误信息
     * @return 邮箱异常
     */
    public static MailboxException invalidParameter(String message) {
        return new MailboxException(400, message);
    }

    /**
     * 创建邮箱创建失败异常。
     *
     * @param message 具体错误信息
     * @return 邮箱异常
     */
    public static MailboxException createFailed(String message) {
        return new MailboxException(500, message);
    }

}