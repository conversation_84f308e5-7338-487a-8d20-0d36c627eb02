package cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.parser;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.bluesking.nanfeng.tools.common.http.ResponseParser;
import cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.dto.CreateMailboxResponse;
import cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.exception.PosteioApiException;

/**
 * 创建邮箱响应解析器。
 *
 * <AUTHOR>
 */
public class CreateMailboxResponseParser implements ResponseParser<CreateMailboxResponse> {

    private static final Logger logger = LoggerFactory.getLogger(CreateMailboxResponseParser.class);

    private final String requestId;

    /**
     * 构造函数。
     *
     * @param requestId 请求 ID。
     */
    public CreateMailboxResponseParser(String requestId) {
        this.requestId = requestId;
    }

    /*-------------------- public method --------------------*/

    @Override
    public CreateMailboxResponse parse(String responseBody) {

        logger.info("parse create mailbox response, requestId: {}, response: {}", requestId, responseBody);
        CreateMailboxResponse response = new CreateMailboxResponse();

        try {

            // 假设响应是成功的 JSON 格式。
            // 实际可能需要根据响应内容进行具体解析。
            response.setSuccess(true);
            response.setMessage("创建邮箱成功");

            // 如果响应中包含 ID 信息，可以解析出来。
            // 这里简单处理，直接返回成功。
            return response;
        }

        catch (Exception e) {
            logger.error("failed to parse create mailbox response, requestId: {}", requestId, e);
            response.setSuccess(false);
            response.setMessage("解析响应失败: " + e.getMessage());
            return response;
        }

    }

    @Override
    public Exception parseError(String responseBody) {
        logger.error("create mailbox error, requestId: {}, response: {}", requestId, responseBody);
        return new PosteioApiException(requestId, responseBody);
    }

    @Override
    public String getRequestId() {
        return requestId;
    }

}