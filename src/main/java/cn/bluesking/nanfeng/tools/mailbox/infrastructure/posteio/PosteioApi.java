package cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio;

import cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.dto.CreateMailboxRequest;
import cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.dto.CreateMailboxResponse;

import reactor.core.publisher.Mono;

/**
 * Poste.io 邮箱 API 接口，定义与 Poste.io 系统交互的方法。
 *
 * <AUTHOR>
 */
public interface PosteioApi {

    /**
     * 创建邮箱。
     *
     * @param request 创建邮箱请求参数。
     * @return 创建结果。
     */
    Mono<CreateMailboxResponse> createMailbox(CreateMailboxRequest request);

}