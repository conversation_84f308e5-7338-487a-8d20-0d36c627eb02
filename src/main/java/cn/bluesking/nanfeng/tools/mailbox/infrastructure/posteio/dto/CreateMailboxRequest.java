package cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.dto;

import java.util.Set;

/**
 * 创建邮箱的请求对象。
 *
 * <AUTHOR>
 */
public class CreateMailboxRequest {

    /**
     * 邮箱名称。
     */
    private String name;

    /**
     * 完整邮箱地址。
     */
    private String email;

    /**
     * 明文密码。
     */
    private String passwordPlaintext;

    /**
     * 是否禁用。
     */
    private Boolean disabled;

    /**
     * 是否是超级管理员。
     */
    private Boolean superAdmin;

    /**
     * 转发目标邮箱列表。
     */
    private Set<String> redirectTo;

    /**
     * 引用 ID。
     */
    private String referenceId;

    /*-------------------- getter --------------------*/

    /**
     * 获取邮箱名称。
     *
     * @return 邮箱名称。
     */
    public String getName() {
        return name;
    }

    /**
     * 获取完整邮箱地址。
     *
     * @return 完整邮箱地址。
     */
    public String getEmail() {
        return email;
    }

    /**
     * 获取明文密码。
     *
     * @return 明文密码。
     */
    public String getPasswordPlaintext() {
        return passwordPlaintext;
    }

    /**
     * 获取是否禁用。
     *
     * @return 是否禁用。
     */
    public Boolean getDisabled() {
        return disabled;
    }

    /**
     * 获取是否是超级管理员。
     *
     * @return 是否是超级管理员。
     */
    public Boolean getSuperAdmin() {
        return superAdmin;
    }

    /**
     * 获取转发目标邮箱列表。
     *
     * @return 转发目标邮箱列表。
     */
    public Set<String> getRedirectTo() {
        return redirectTo;
    }

    /**
     * 获取引用 ID。
     *
     * @return 引用 ID。
     */
    public String getReferenceId() {
        return referenceId;
    }

    /*-------------------- setter --------------------*/

    /**
     * 设置邮箱名称。
     *
     * @param name 邮箱名称。
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 设置完整邮箱地址。
     *
     * @param email 完整邮箱地址。
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 设置明文密码。
     *
     * @param passwordPlaintext 明文密码。
     */
    public void setPasswordPlaintext(String passwordPlaintext) {
        this.passwordPlaintext = passwordPlaintext;
    }

    /**
     * 设置是否禁用。
     *
     * @param disabled 是否禁用。
     */
    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    /**
     * 设置是否是超级管理员。
     *
     * @param superAdmin 是否是超级管理员。
     */
    public void setSuperAdmin(Boolean superAdmin) {
        this.superAdmin = superAdmin;
    }

    /**
     * 设置转发目标邮箱列表。
     *
     * @param redirectTo 转发目标邮箱列表。
     */
    public void setRedirectTo(Set<String> redirectTo) {
        this.redirectTo = redirectTo;
    }

    /**
     * 设置引用 ID。
     *
     * @param referenceId 引用 ID。
     */
    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "{\"CreateMailboxRequest\":{" +
                   "\"name\": \"" + name + '\"' +
                   ", \"email\": \"" + email + '\"' +
                   ", \"passwordPlaintext\": \"" + passwordPlaintext + '\"' +
                   ", \"disabled\": " + disabled +
                   ", \"superAdmin\": " + superAdmin +
                   ", \"redirectTo\": " + redirectTo +
                   ", \"referenceId\": \"" + referenceId + '\"' +
                   "}}";
    }

}