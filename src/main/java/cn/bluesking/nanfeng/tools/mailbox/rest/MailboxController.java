package cn.bluesking.nanfeng.tools.mailbox.rest;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.bluesking.nanfeng.tools.common.web.Response;
import cn.bluesking.nanfeng.tools.config.properties.PosteioApiProperties;
import cn.bluesking.nanfeng.tools.mailbox.service.MailboxService;
import cn.bluesking.nanfeng.tools.mailbox.service.dto.CreateRedirectMailboxParams;

/**
 * 邮件箱控制器，处理与邮件箱相关的请求。
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mailbox")
public class MailboxController {

    private final MailboxService mailboxService;

    /**
     * 邮箱后缀配置。
     */
    private final String emailSuffix;

    /**
     * 构造函数。
     *
     * @param mailboxService 邮件箱服务
     * @param posteioApiProperties Posteio API 配置
     */
    public MailboxController(MailboxService mailboxService, PosteioApiProperties posteioApiProperties) {
        this.mailboxService = mailboxService;
        this.emailSuffix = posteioApiProperties.getEmailSuffix();
    }

    /*-------------------- public method --------------------*/

    /**
     * 获取邮箱后缀配置。
     *
     * @return 邮箱后缀
     */
    @GetMapping("/config/emailSuffix")
    public String getEmailSuffix() {
        return emailSuffix;
    }

    /**
     * 创建重定向邮箱。
     *
     * @param params 创建重定向邮箱的参数
     * @return 创建结果
     */
    @PostMapping("/redirect")
    public Response<Boolean> createRedirectMailbox(@RequestBody CreateRedirectMailboxParams params) {

        // 由于已经有全局异常处理，方法直接调用服务即可，异常会被自动处理。
        boolean result = mailboxService.createRedirectMailbox(params);
        // 构建完整的邮箱地址
        String fullEmailAddress = params.getMailboxName() + "@" + emailSuffix;
        // 构建成功消息
        return Response.success(String.format("创建重定向邮箱『%s』成功！", fullEmailAddress), result);
    }

}