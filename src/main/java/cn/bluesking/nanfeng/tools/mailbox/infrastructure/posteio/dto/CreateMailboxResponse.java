package cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.dto;

/**
 * 创建重定向邮箱的响应对象。
 *
 * <AUTHOR>
 */
public class CreateMailboxResponse {

    /**
     * 操作是否成功。
     */
    private boolean success;

    /**
     * 响应消息。
     */
    private String message;

    /**
     * 邮箱 ID。
     */
    private String id;

    /*-------------------- getter --------------------*/

    /**
     * 获取操作是否成功。
     *
     * @return 操作是否成功。
     */
    public boolean isSuccess() {
        return success;
    }

    /**
     * 获取响应消息。
     *
     * @return 响应消息。
     */
    public String getMessage() {
        return message;
    }

    /**
     * 获取邮箱 ID。
     *
     * @return 邮箱 ID。
     */
    public String getId() {
        return id;
    }

    /*-------------------- setter --------------------*/

    /**
     * 设置操作是否成功。
     *
     * @param success 操作是否成功。
     */
    public void setSuccess(boolean success) {
        this.success = success;
    }

    /**
     * 设置响应消息。
     *
     * @param message 响应消息。
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 设置邮箱 ID。
     *
     * @param id 邮箱 ID。
     */
    public void setId(String id) {
        this.id = id;
    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "{\"CreateMailboxResponse\":{" +
                   "\"success\": " + success +
                   ", \"message\": \"" + message + '\"' +
                   ", \"id\": \"" + id + '\"' +
                   "}}";
    }

}