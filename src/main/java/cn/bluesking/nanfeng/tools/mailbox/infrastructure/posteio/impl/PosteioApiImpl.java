package cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.impl;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import cn.bluesking.nanfeng.tools.common.http.ApiClient;
import cn.bluesking.nanfeng.tools.common.http.ApiClientFactory;
import cn.bluesking.nanfeng.tools.common.http.ContentType;
import cn.bluesking.nanfeng.tools.common.utils.JsonUtils;
import cn.bluesking.nanfeng.tools.common.utils.UuidGenerator;
import cn.bluesking.nanfeng.tools.config.properties.PosteioApiProperties;
import cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.PosteioApi;
import cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.dto.CreateMailboxRequest;
import cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.dto.CreateMailboxResponse;
import cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.exception.PosteioApiException;
import cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.parser.CreateMailboxResponseParser;

import reactor.core.publisher.Mono;

/**
 * Poste.io API 接口实现。
 *
 * <AUTHOR>
 */
@Service
class PosteioApiImpl implements PosteioApi {

    private static final Logger logger = LoggerFactory.getLogger(PosteioApiImpl.class);

    private final PosteioApiProperties config;

    private final ApiClient apiClient;

    /**
     * 构造函数。
     *
     * @param config Poste.io API 配置。
     */
    public PosteioApiImpl(@Qualifier("mailboxPosteioApiProperties") PosteioApiProperties config) {
        this.config = config;
        this.apiClient = ApiClientFactory.create();
    }

    /*-------------------- public method --------------------*/

    /**
     * 创建邮箱。
     *
     * @param request 创建邮箱请求参数。
     * @return 创建结果。
     */
    @Override
    public Mono<CreateMailboxResponse> createMailbox(CreateMailboxRequest request) {

        if (request == null) {
            logger.error("create mailbox failed, request is null");
            return Mono.error(new IllegalArgumentException("创建邮箱请求参数不能为空"));
        }

        String requestId = UuidGenerator.generate();
        logger.info("start to create mailbox, requestId: {}, request: {}", requestId, request);

        try {
            // 如果邮箱地址为空，使用邮箱名称和配置的域名后缀构建
            if (request.getEmail() == null || request.getEmail().trim().isEmpty()) {
                request.setEmail(request.getName() + "@" + config.getEmailSuffix());
            }

            // 构建请求头，包含 Basic 认证信息。
            Map<String, String> headers = buildHeaders();

            // 构建 URL。
            String url = config.getBaseUrl() + "/admin/api/v1/boxes";

            // 构建响应解析器。
            CreateMailboxResponseParser parser = new CreateMailboxResponseParser(requestId);

            // 发送请求。
//            return apiClient.post(url, JsonUtils.toJson(request), headers, parser);
            return apiClient.request()
                       .uri(url)
                       .post()
                       .bodyJson(request)
                       .headers(headers)
                       .retrieve(parser);
        }

        catch (Exception e) {
            logger.error("failed to create mailbox, requestId: {}", requestId, e);
            return Mono.error(new PosteioApiException(requestId, e));
        }

    }

    /*-------------------- private method --------------------*/

    /**
     * 构建请求头。
     *
     * @return 请求头。
     */
    private Map<String, String> buildHeaders() {

        Map<String, String> headers = new HashMap<>();

        // 设置 Content-Type。
        headers.put("Content-Type", ContentType.JSON.getValueWithCharset(StandardCharsets.UTF_8));
        headers.put("Accept", "*/*");

        // 设置 Basic 认证。
        String auth = config.getUsername() + ":" + config.getPassword();
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
        headers.put("Authorization", "Basic " + encodedAuth);

        return headers;
    }

}