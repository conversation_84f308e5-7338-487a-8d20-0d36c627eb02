package cn.bluesking.nanfeng.tools.mailbox.infrastructure.posteio.exception;

/**
 * Posteio API 调用中的异常情况。
 *
 * <AUTHOR>
 */
public class PosteioApiException extends RuntimeException {

    private final String requestId;

    public PosteioApiException(String requestId, Throwable cause) {
        super(cause);
        this.requestId = requestId;
    }

    public PosteioApiException(String requestId, String message) {
        super(message);
        this.requestId = requestId;
    }

    /*-------------------- getter --------------------*/

    public String getRequestId() {
        return requestId;
    }

}