package cn.bluesking.nanfeng.tools.mailbox.service.dto;

import java.util.List;
import java.util.Set;

/**
 * 创建重定向邮箱的参数类。
 *
 * <AUTHOR>
 */
public class CreateRedirectMailboxParams {

    /**
     * 邮箱名称
     */
    private String mailboxName;

    /**
     * 转发目标邮箱集合
     */
    private Set<String> redirectToEmailAddresses;

    /*-------------------- getter --------------------*/

    /**
     * 获取邮箱名称
     *
     * @return 邮箱名称
     */
    public String getMailboxName() {
        return mailboxName;
    }

    /**
     * 获取转发目标邮箱集合
     *
     * @return 转发目标邮箱集合
     */
    public Set<String> getRedirectToEmailAddresses() {
        return redirectToEmailAddresses;
    }

    /*-------------------- setter --------------------*/

    /**
     * 设置邮箱名称
     *
     * @param mailboxName 邮箱名称
     */
    public void setMailboxName(String mailboxName) {
        this.mailboxName = mailboxName;
    }

    /**
     * 设置转发目标邮箱集合
     *
     * @param redirectToEmailAddresses 转发目标邮箱集合
     */
    public void setRedirectToEmailAddresses(Set<String> redirectToEmailAddresses) {
        this.redirectToEmailAddresses = redirectToEmailAddresses;
    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "CreateRedirectMailBoxParams{" +
                   "mailboxName='" + mailboxName + '\'' +
                   ", redirectToEmailAddresses=" + redirectToEmailAddresses +
                   '}';
    }

}