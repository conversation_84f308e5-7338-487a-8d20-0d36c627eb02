package cn.bluesking.nanfeng.tools.work.edi.stats;

import java.util.Objects;

import cn.bluesking.nanfeng.tools.work.common.annotation.ExcelTitles;

/**
 * 最终写入输出文件的 EDI 记录。
 *
 * <AUTHOR>
 */
public class OutputEdiRecord {

    /**
     * 到港日期
     */
    @ExcelTitles("日期")
    private String arrivalDate;

    /**
     * 船名
     */
    @ExcelTitles("船名")
    private String bargeName;

    /**
     * 航次
     */
    @ExcelTitles("航次")
    private String bargeVoyage;

    /**
     * 船公司
     */
    @ExcelTitles("船公司")
    private String bargeCompanyName;

    /**
     * 提单号
     */
    @ExcelTitles("SO")
    private String ladeBillNo;

    /**
     * 货名
     */
    @ExcelTitles("货名")
    private String cargoName;

    /**
     * 20 尺寸数量，值为：1。
     */
    @ExcelTitles("20")
    private Integer size20Count;

    /**
     * 40 尺寸数量，值为：1。
     */
    @ExcelTitles("40")
    private Integer size40Count;

    /*-------------------- getter --------------------*/

    /**
     * 获取到港日期。
     *
     * @return 到港日期
     */
    public String getArrivalDate() {
        return arrivalDate;
    }

    /**
     * 获取船名。
     *
     * @return 船名
     */
    public String getBargeName() {
        return bargeName;
    }

    /**
     * 获取航次。
     *
     * @return 航次
     */
    public String getBargeVoyage() {
        return bargeVoyage;
    }

    /**
     * 获取船公司。
     *
     * @return 船公司
     */
    public String getBargeCompanyName() {
        return bargeCompanyName;
    }

    /**
     * 获取提单号。
     *
     * @return 提单号
     */
    public String getLadeBillNo() {
        return ladeBillNo;
    }

    /**
     * 获取货名。
     *
     * @return 货名
     */
    public String getCargoName() {
        return cargoName;
    }

    /**
     * 获取 20 尺寸数量。
     *
     * @return 20 尺寸数量
     */
    public Integer getSize20Count() {
        return size20Count;
    }

    /**
     * 获取 40 尺寸数量。
     *
     * @return 40 尺寸数量
     */
    public Integer getSize40Count() {
        return size40Count;
    }

    /*-------------------- setter --------------------*/

    /**
     * 设置到港日期。
     *
     * @param arrivalDate 到港日期
     */
    public void setArrivalDate(String arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    /**
     * 设置船名。
     *
     * @param bargeName 船名
     */
    public void setBargeName(String bargeName) {
        this.bargeName = bargeName;
    }

    /**
     * 设置航次。
     *
     * @param bargeVoyage 航次
     */
    public void setBargeVoyage(String bargeVoyage) {
        this.bargeVoyage = bargeVoyage;
    }

    /**
     * 设置船公司。
     *
     * @param bargeCompanyName 船公司
     */
    public void setBargeCompanyName(String bargeCompanyName) {
        this.bargeCompanyName = bargeCompanyName;
    }

    /**
     * 设置提单号。
     *
     * @param ladeBillNo 提单号
     */
    public void setLadeBillNo(String ladeBillNo) {
        this.ladeBillNo = ladeBillNo;
    }

    /**
     * 设置货名。
     *
     * @param cargoName 货名
     */
    public void setCargoName(String cargoName) {
        this.cargoName = cargoName;
    }

    /**
     * 设置 20 尺寸数量。
     *
     * @param size20Count 20 尺寸数量
     */
    public void setSize20Count(Integer size20Count) {
        this.size20Count = size20Count;
    }

    /**
     * 设置 40 尺寸数量。
     *
     * @param size40Count 40 尺寸数量
     */
    public void setSize40Count(Integer size40Count) {
        this.size40Count = size40Count;
    }

    /*-------------------- Object methods --------------------*/

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        OutputEdiRecord that = (OutputEdiRecord) o;
        return Objects.equals(arrivalDate, that.arrivalDate) &&
                   Objects.equals(bargeName, that.bargeName) &&
                   Objects.equals(bargeVoyage, that.bargeVoyage) &&
                   Objects.equals(bargeCompanyName, that.bargeCompanyName) &&
                   Objects.equals(ladeBillNo, that.ladeBillNo) &&
                   Objects.equals(cargoName, that.cargoName) &&
                   Objects.equals(size20Count, that.size20Count) &&
                   Objects.equals(size40Count, that.size40Count);
    }

    @Override
    public int hashCode() {
        return Objects.hash(arrivalDate, bargeName, bargeVoyage, bargeCompanyName,
            ladeBillNo, cargoName, size20Count, size40Count);
    }

    @Override
    public String toString() {
        return "OutputEdiRecord{" +
                   "arrivalDate='" + arrivalDate + '\'' +
                   ", bargeName='" + bargeName + '\'' +
                   ", bargeVoyage='" + bargeVoyage + '\'' +
                   ", bargeCompanyName='" + bargeCompanyName + '\'' +
                   ", ladeBillNo='" + ladeBillNo + '\'' +
                   ", cargoName='" + cargoName + '\'' +
                   ", size20Count=" + size20Count +
                   ", size40Count=" + size40Count +
                   '}';
    }

}