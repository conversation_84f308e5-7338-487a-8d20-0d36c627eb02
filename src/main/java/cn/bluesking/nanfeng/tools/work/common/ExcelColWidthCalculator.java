package cn.bluesking.nanfeng.tools.work.common;

import java.nio.charset.Charset;
import java.util.function.Predicate;

import org.apache.commons.lang3.StringUtils;

/**
 * Excel 列宽度计算器。
 *
 * <AUTHOR>
 */
class ExcelColWidthCalculator {

    private static final double CHINESE_RATE = 256 * 1.95;

    private static final double UPPER_CASE_LETTER_RATE = 256 * 1.27;

    private static final double LOWER_CASE_LETTER_RATE = 256 * 1.03;

    private static final double BLANK_RATE = 256 * 0.5;

    private static final double NUMBER_RATE = 256 * 1.0;

    private static final double OTHER_CHAR_RATE = 256 * 1.0;

    private static final int EXTRA_WIDTH = 256 * 2;

    public int calcWith(String data) {

        if (StringUtils.isEmpty(data)) {
            return 0;
        }

        else {
            char[] chars = data.toCharArray();
            int charCount = chars.length;
            int chineseCharCount = getChineseCharCount(data, charCount);
            int upperCaseLetterCount = getSpecialLetterCount(chars, c -> 'A' <= c && c <= 'Z');
            int lowerCaseLetterCount = getSpecialLetterCount(chars, c -> 'a' <= c && c <= 'z');
            int blankCount = getSpecialLetterCount(chars, c -> c == ' ');
            int numberCount = getSpecialLetterCount(chars, c -> '0' <= c && c <= '9');
            int otherCharCount = charCount - chineseCharCount -
                                     upperCaseLetterCount - lowerCaseLetterCount - blankCount - numberCount;

            return (int) (chineseCharCount * CHINESE_RATE +
                              upperCaseLetterCount * UPPER_CASE_LETTER_RATE +
                              lowerCaseLetterCount * LOWER_CASE_LETTER_RATE +
                              blankCount * BLANK_RATE +
                              numberCount * NUMBER_RATE +
                              otherCharCount * OTHER_CHAR_RATE) + EXTRA_WIDTH;
        }

    }

    private int getChineseCharCount(String data, int charCount) {

        int byteCount = data.getBytes(Charset.forName("GBK")).length;
        return byteCount - charCount;
    }

    private int getSpecialLetterCount(char[] chars, Predicate<Character> condition) {

        int count = 0;
        for (char c : chars) {
            if (condition.test(c)) {
                count++;
            }

        }

        return count;
    }

}