package cn.bluesking.nanfeng.tools.work.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 映射 Excel 的标题头。
 *
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelTitles {

    /**
     * 标题头集合。
     */
    String[] value() default {};

    /**
     * 读取时标题头集合匹配方式。
     */
    MatchMode getReadMatchMode() default MatchMode.EQUAL;

    /**
     * Excel 标题头匹配模式。
     *
     * <AUTHOR>
     */
    enum MatchMode {

        /**
         * 相等
         */
        EQUAL,

        /**
         * 包含
         */
        CONTAIN,

        /**
         * 正则表达式
         */
        REGEX
    }

}