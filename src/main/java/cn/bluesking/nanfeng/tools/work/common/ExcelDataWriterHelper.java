package cn.bluesking.nanfeng.tools.work.common;

import static cn.bluesking.nanfeng.tools.common.utils.ValidationUtils.assertNotNull;
import static cn.bluesking.nanfeng.tools.common.utils.ValidationUtils.assertTrue;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import cn.bluesking.nanfeng.tools.work.common.annotation.ExcelTitles;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 通用的 Excel 数据写出器助手类。
 *
 * <AUTHOR>
 */
public final class ExcelDataWriterHelper {

    private static final Logger logger = LoggerFactory.getLogger(ExcelDataWriterHelper.class);

    private static final ExcelColWidthCalculator widthCalculator = new ExcelColWidthCalculator();

    private ExcelDataWriterHelper() {
    }

    public static <T> void write(File file, List<T> elements, String[] sortedFieldNames) {

        if (CollectionUtils.isNotEmpty(elements) && ArrayUtils.isNotEmpty(sortedFieldNames)) {

            assertNotNull(file);
            assertTrue((file.exists() && file.isFile()) || !file.exists());

            String fileName = file.getName();
            String fileType = fileName.substring(fileName.lastIndexOf('.') + 1);

            try (
                Workbook workbook = createWorkbook(fileType);
                FileOutputStream fos = new FileOutputStream(file)
            ) {

                CellStyle cellStyle = createCellStyle(workbook);

                int[] maxColWidths = new int[sortedFieldNames.length];
                Field[] fields = sortedFields(elements.get(0).getClass(), sortedFieldNames);

                AtomicInteger rowIndex = new AtomicInteger(0);
                Sheet sheet = workbook.createSheet();

                writeTitle(rowIndex, cellStyle, sheet, sortedFieldNames);
                for (T element : elements) {

                    if (Objects.nonNull(element)) {

                        Row row = sheet.createRow(rowIndex.getAndIncrement());
                        writeElementToRow(element, cellStyle, row, maxColWidths, fields);
                    }
                }

                setColumnMaxWidth(sheet, maxColWidths);
                workbook.write(fos);
            }
            catch (IOException e) {
                throw new DataWriterException(e);
            }

        }

    }

    private static CellStyle createCellStyle(Workbook workbook) {

        CellStyle columnStyle = workbook.createCellStyle();

        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);

        columnStyle.setFont(font);
        columnStyle.setAlignment(HorizontalAlignment.CENTER);

        return columnStyle;
    }

    private static void setColumnMaxWidth(Sheet sheet, int[] maxColWidths) {

        for (int i = 0, length = maxColWidths.length; i < length; i++) {
            if (maxColWidths[i] > 0) {
                sheet.setColumnWidth(i, maxColWidths[i]);
            }
        }
    }

    private static <T> void writeElementToRow(T element,
                                              CellStyle cellStyle,
                                              Row row,
                                              int[] maxColWidths,
                                              Field[] fields) {

        for (int i = 0, length = fields.length; i < length; i++) {

            Field field = fields[i];
            Cell cell = row.createCell(i);
            cell.setCellStyle(cellStyle);
            if (Objects.nonNull(field)) {

                Object fieldValue = getFieldValue(element, field);
                if (Objects.nonNull(fieldValue)) {

                    String fieldValueStr = String.valueOf(fieldValue);
                    maxColWidths[i] = Math.max(maxColWidths[i], widthCalculator.calcWith(fieldValueStr));
                    if (fieldValue instanceof Number) {
                        cell.setCellValue(Double.parseDouble(fieldValueStr));
                    }
                    else {
                        cell.setCellValue(fieldValueStr);
                    }
                }
            }
        }
    }

    private static Object getFieldValue(Object element, Field field) {

        try {
            return field.get(element);
        }
        catch (IllegalAccessException e) {
            logger.error("failed to get field value.", e);
            return null;
        }

    }

    @SuppressWarnings("java:S3011")
    private static <T> Field[] sortedFields(Class<T> cls, String[] sortedFieldNames) {

        Map<String, Integer> fieldNameToIndex = new HashMap<>();
        for (int i = 0, length = sortedFieldNames.length; i < length; i++) {
            fieldNameToIndex.put(sortedFieldNames[i], i);
        }

        Field[] sortedFields = new Field[sortedFieldNames.length];

        Field[] fields = cls.getDeclaredFields();
        for (Field field : fields) {

            if (field.isAnnotationPresent(ExcelTitles.class)) {

                field.setAccessible(true);
                ExcelTitles excelTitles = field.getDeclaredAnnotation(ExcelTitles.class);
                if (excelTitles != null) {

                    String[] titles = ArrayUtils.nullToEmpty(excelTitles.value());
                    for (String title : titles) {

                        Integer index = fieldNameToIndex.get(title);
                        if (Objects.nonNull(index) && Objects.isNull(sortedFields[index])) {

                            sortedFields[index] = field;
                        }
                    }
                }
            }
        }

        return sortedFields;
    }

    private static void writeTitle(AtomicInteger rowIndex,
                                   CellStyle cellStyle,
                                   Sheet sheet,
                                   String[] sortedFieldNames) {

        Row titleRow = sheet.createRow(rowIndex.getAndIncrement());
        for (int i = 0, length = sortedFieldNames.length; i < length; i++) {
            Cell cell = titleRow.createCell(i);
            cell.setCellValue(sortedFieldNames[i]);
            cell.setCellStyle(cellStyle);
        }

    }

    private static Workbook createWorkbook(String fileType) {

        if (StringUtils.equalsIgnoreCase(fileType, "xls")) {
            return new HSSFWorkbook();
        }
        else if (StringUtils.equalsIgnoreCase(fileType, "xlsx")) {
            return new XSSFWorkbook();
        }
        else {
            throw new IllegalArgumentException(fileType);
        }

    }

    /**
     * 数据写出器异常。
     *
     * <AUTHOR>
     */
    public static class DataWriterException extends RuntimeException {

        public DataWriterException(Throwable cause) {
            super(cause);
        }

    }

}