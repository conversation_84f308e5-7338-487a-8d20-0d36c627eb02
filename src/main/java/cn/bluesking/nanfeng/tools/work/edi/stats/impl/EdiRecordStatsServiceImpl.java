package cn.bluesking.nanfeng.tools.work.edi.stats.impl;

import java.io.File;
import java.text.Collator;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import cn.bluesking.nanfeng.tools.common.utils.DateUtils;
import cn.bluesking.nanfeng.tools.work.common.ExcelDataReaderHelper;
import cn.bluesking.nanfeng.tools.work.common.ExcelDataWriterHelper;
import cn.bluesking.nanfeng.tools.work.edi.stats.EdiRecordStatsService;
import cn.bluesking.nanfeng.tools.work.edi.stats.InputEdiRecord;
import cn.bluesking.nanfeng.tools.work.edi.stats.OutputEdiRecord;

/**
 * Edi 记录统计服务实现类。
 *
 * <AUTHOR>
 */
@Service
class EdiRecordStatsServiceImpl implements EdiRecordStatsService {

    private static final String OUTPUT_FILE_NAME_PREFIX = "EDI-统计结果";

    private final String tmpdir;

    public EdiRecordStatsServiceImpl() {
        this.tmpdir = System.getProperty("java.io.tmpdir");
    }

    @Override
    public File export(List<File> files) {

        List<File> inputs = Optional.ofNullable(files)
                                .orElseGet(Collections::emptyList)
                                .stream()
                                .filter(this::isInputFile)
                                .sorted((f1, f2) -> Collator.getInstance(Locale.CHINA).compare(f1.getName(), f2.getName()))
                                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(inputs)) {
            return null;
        }

        else {

            String outputFilePath = String.format("%s%s-%s.xls",
                tmpdir, OUTPUT_FILE_NAME_PREFIX, DateUtils.formatCompactDateTime(LocalDateTime.now()));
            File output = new File(outputFilePath);

            List<OutputEdiRecord> outputEdiRecords = new ArrayList<>();
            for (File input : inputs) {

                List<InputEdiRecord> inputEdiRecords = ExcelDataReaderHelper.read(input, InputEdiRecord.class);
                outputEdiRecords.addAll(statsToEdiRecords(inputEdiRecords));
            }

            ExcelDataWriterHelper.write(output, outputEdiRecords, new String[] {
                "日期", "船名", "航次", "船公司", "SO", "货名", "20", "40"
            });

            return output;
        }

    }

    private boolean isInputFile(File file) {

        if (file == null || !file.isFile()) {
            return false;
        }

        else {
            String fileName = file.getName();
            return !StringUtils.contains(fileName, OUTPUT_FILE_NAME_PREFIX)
                       && StringUtils.endsWithAny(fileName, ".xls", ".xlsx");
        }

    }

    private static List<OutputEdiRecord> statsToEdiRecords(List<InputEdiRecord> inputRecords) {

        List<OutputEdiRecord> outputEdiRecords = new ArrayList<>();

        LinkedHashMap<String, List<InputEdiRecord>> byContainerNumberEdis = inputRecords
                                                                                .stream()
                                                                                .filter(
                                                                                    inputEdiRecord -> StringUtils.isNotEmpty(
                                                                                        inputEdiRecord.getLadeBillNo()))
                                                                                .collect(Collectors.groupingBy(
                                                                                    InputEdiRecord::getLadeBillNo,
                                                                                    LinkedHashMap::new,
                                                                                    Collectors.toList()));

        for (List<InputEdiRecord> sameContainerNumberInputEdiRecords : byContainerNumberEdis.values()) {

            InputEdiRecord firstInputRecord = sameContainerNumberInputEdiRecords.get(0);
            OutputEdiRecord outputRecord = new OutputEdiRecord();

            outputRecord.setBargeName(firstInputRecord.getBargeName());
            outputRecord.setBargeVoyage(firstInputRecord.getBargeVoyage());
            outputRecord.setLadeBillNo(firstInputRecord.getLadeBillNo());
            outputRecord.setCargoName(firstInputRecord.getCargoName());

            sameContainerNumberInputEdiRecords
                .stream()
                .filter(e -> StringUtils.isNotEmpty(e.getContainerSize()))
                .collect(Collectors.groupingBy(InputEdiRecord::getContainerSize))
                .forEach((containerSize, subEdis) -> {

                    if (containerSize.startsWith("2")) {
                        outputRecord.setSize20Count(addIgnoreNull(outputRecord.getSize20Count(), subEdis.size()));
                    }

                    else if (containerSize.startsWith("4")) {
                        outputRecord.setSize40Count(addIgnoreNull(outputRecord.getSize40Count(), subEdis.size()));
                    }

                });

            outputEdiRecords.add(outputRecord);
        }

        return outputEdiRecords;
    }

    private static Integer addIgnoreNull(Integer raw, int incr) {
        return raw == null ? incr : raw + incr;
    }

}