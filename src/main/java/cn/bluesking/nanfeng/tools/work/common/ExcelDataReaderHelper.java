package cn.bluesking.nanfeng.tools.work.common;

import static cn.bluesking.nanfeng.tools.common.utils.ValidationUtils.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 通用的 Excel 数据读取器助手类。
 *
 * <AUTHOR>
 */
public final class ExcelDataReaderHelper {

    private static final Logger logger = LoggerFactory.getLogger(ExcelDataReaderHelper.class);

    private ExcelDataReaderHelper() {
    }

    public static <T> List<T> read(File file, Class<T> targetClass) {

        assertNotNull(file);
        assertTrue(file.exists());
        assertTrue(file.isFile());

        String fileName = file.getName();
        String fileType = fileName.substring(fileName.lastIndexOf('.') + 1);

        List<T> entities = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(file)) {

            Workbook workbook = createWorkbook(fis, fileType);

            int activeSheetIndex = workbook.getActiveSheetIndex();
            Sheet sheet = workbook.getSheetAt(activeSheetIndex);

            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();

            Row titleRow = sheet.getRow(firstRowNum);
            Row2EntityConverter<T> entityConverter = new Row2EntityConverter<>(targetClass, titleRow);

            if (entityConverter.isValid()) {
                for (int i = firstRowNum + 1; i <= lastRowNum; i++) {

                    Row row = sheet.getRow(i);
                    if (row != null) {

                        T entity = entityConverter.convert(row);
                        if (entity != null) {
                            entities.add(entity);
                        }
                    }
                }
            }
        }
        catch (IOException e) {
            throw new DataReaderException(e);
        }

        return entities;
    }

    private static Workbook createWorkbook(InputStream is, String fileType) throws IOException {

        if (StringUtils.equalsIgnoreCase(fileType, "xls")) {
            return new HSSFWorkbook(is);
        }
        else if (StringUtils.equalsIgnoreCase(fileType, "xlsx")) {
            return new XSSFWorkbook(is);
        }
        else {
            throw new IllegalArgumentException(fileType);
        }

    }

    /**
     * 数据读取器异常。
     *
     * <AUTHOR>
     */
    public static class DataReaderException extends RuntimeException {

        public DataReaderException(String message, Throwable cause) {
            super(message, cause);
        }

        public DataReaderException(Throwable cause) {
            super(cause);
        }

    }

}