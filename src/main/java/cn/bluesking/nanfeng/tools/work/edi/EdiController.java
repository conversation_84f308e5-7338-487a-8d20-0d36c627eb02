package cn.bluesking.nanfeng.tools.work.edi;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import cn.bluesking.nanfeng.tools.common.utils.CompressedFileUtils;
import cn.bluesking.nanfeng.tools.common.utils.UuidGenerator;
import cn.bluesking.nanfeng.tools.work.edi.stats.EdiRecordStatsService;

/**
 * Edi 相关接口。
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/edi")
public class EdiController {

    private static final String TMPDIR = System.getProperty("java.io.tmpdir");

    private final EdiRecordStatsService ediRecordStatsService;

    public EdiController(EdiRecordStatsService ediRecordStatsService) {
        this.ediRecordStatsService = ediRecordStatsService;
    }

    @PostMapping("/stats")
    public ResponseEntity<byte[]> stats(@RequestParam("file") MultipartFile multipartFile) throws IOException {

        List<File> inputs = uncompressedFile(multipartFile);
        File output = ediRecordStatsService.export(inputs);
        try {

            HttpHeaders headers = new HttpHeaders();
            headers.setContentDispositionFormData("attachment",
                URLEncoder.encode(output.getName(), StandardCharsets.UTF_8));
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            return new ResponseEntity<>(FileUtils.readFileToByteArray(output), headers, HttpStatus.OK);
        }

        finally {
            FileUtils.deleteQuietly(output);
            if (CollectionUtils.isNotEmpty(inputs)) {
                FileUtils.deleteDirectory(inputs.get(0).getParentFile());
            }

        }

    }

    private List<File> uncompressedFile(MultipartFile multipartFile) throws IOException {

        String uuid = UuidGenerator.generate();
        String compressedFilePath = TMPDIR + uuid + "-" + multipartFile.getOriginalFilename();
        File compressedFile = new File(compressedFilePath);

        try {

            multipartFile.transferTo(compressedFile);
            return CompressedFileUtils.unzipFiles(compressedFile, TMPDIR + uuid + "-未录入重柜/");
        }

        finally {
            FileUtils.deleteQuietly(compressedFile);
        }

    }

}