package cn.bluesking.nanfeng.tools.work.common;

import static cn.bluesking.nanfeng.tools.common.utils.ValidationUtils.assertNull;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 */
class ColumnReflection<T> {

    private final Map<String, BiConsumer<T, Object>> titleToSetterMethod;

    public ColumnReflection() {
        this.titleToSetterMethod = Collections.synchronizedMap(new HashMap<>());
    }

    public void addRef(String[] titles, BiConsumer<T, Object> setter) {

        for (String title : titles) {

            BiConsumer<T, Object> returnedSetter = titleToSetterMethod.putIfAbsent(title, setter);
            assertNull(returnedSetter, () -> String.format("标题『%s』存在重复配置！", title));
        }

    }

    public BiConsumer<T, Object> getSetter(String title) {
        return titleToSetterMethod.get(title);
    }

}