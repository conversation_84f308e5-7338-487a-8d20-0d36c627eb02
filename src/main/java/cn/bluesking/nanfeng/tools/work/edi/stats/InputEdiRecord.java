package cn.bluesking.nanfeng.tools.work.edi.stats;

import java.util.Objects;

import cn.bluesking.nanfeng.tools.work.common.annotation.ExcelTitles;

/**
 * 输入的 EDI 记录。
 * <p>海运中的 EDI 是英文 Electronic Data Interchange 的缩写，中文可译为『电子数据交换』。它是一种在公司之间传输订单、发票等作业文件的电子化。
 * 手段。
 *
 * <AUTHOR>
 */
public class InputEdiRecord {

    /**
     * 驳船船名，值为：柏福238。
     */
    @ExcelTitles("船名")
    private String bargeName;

    /**
     * 驳船航次，值为：520102207220。
     */
    @ExcelTitles("航次")
    private String bargeVoyage;

    /**
     * 集装箱提单票据编号，值为：219353526。
     */
    @ExcelTitles("提单号")
    private String ladeBillNo;

    /**
     * 装货港，值为：HKHKG。
     */
    @ExcelTitles("装货港")
    private String loadPort;

    /**
     * 中转港，值为空。
     */
    @ExcelTitles("中转港")
    private String transferPort;

    /**
     * 卸货港，值为：CNHUA。
     */
    @ExcelTitles("卸货港（目的港）")
    private String dischargePort;

    /**
     * 集装箱编号，值为：BSIU3058214。
     */
    @ExcelTitles("箱号")
    private String containerNo;

    /**
     * 集装箱尺寸类型，值为：<ul>。
     * <li>22G1</li>
     * <li>45G1</li>
     * </ul>
     */
    @ExcelTitles("尺寸类型")
    private String containerSize;

    /**
     * 空重，值为：<ul>。
     * <li>F</li>
     * </ul>
     */
    @ExcelTitles("空重")
    private String emptyWeight;

    /**
     * 重控，值为：20072。
     */
    @ExcelTitles("重控")
    private String zControl;

    /**
     * 吉控，值为：12310。
     */
    @ExcelTitles("吉控")
    private String jControl;

    /**
     * 铅封号，值为：MM0191786。
     */
    @ExcelTitles("铅封号")
    private String sealNo;

    /**
     * 交货条款，值为：<ul>。
     * <li>CY/CY</li>
     * </ul>
     */
    @ExcelTitles("交货条款")
    private String deliveryClause;

    /**
     * 收货人，值为空。
     */
    @ExcelTitles("收货人")
    private String consignee;

    /**
     * 货物名称，值为：缅甸碎白米。
     */
    @ExcelTitles("货名")
    private String cargoName;

    /**
     * 件数，值为：500.0。
     */
    @ExcelTitles("件数")
    private String packageCount;

    /**
     * 是否包装，值为：<ul>。
     * <li>包</li>
     * <li>其他</li>
     * </ul>
     */
    @ExcelTitles("包装")
    private String wrap;

    /**
     * 重量，值为：25060。
     */
    @ExcelTitles("重量")
    private String weight;

    /**
     * 体积，值为空。
     */
    @ExcelTitles("体积")
    private String volume;

    /**
     * 冷藏温度，值为空。
     */
    @ExcelTitles("冷藏温度")
    private String refrigeratedTemperature;

    /**
     * 危险品等级，值为空。
     */
    @ExcelTitles("危险品等级")
    private String dangerousGoodsClass;

    /*-------------------- getter --------------------*/

    /**
     * 获取驳船船名。
     *
     * @return 驳船船名
     */
    public String getBargeName() {
        return bargeName;
    }

    /**
     * 获取驳船航次。
     *
     * @return 驳船航次
     */
    public String getBargeVoyage() {
        return bargeVoyage;
    }

    /**
     * 获取集装箱提单票据编号。
     *
     * @return 集装箱提单票据编号
     */
    public String getLadeBillNo() {
        return ladeBillNo;
    }

    /**
     * 获取装货港。
     *
     * @return 装货港
     */
    public String getLoadPort() {
        return loadPort;
    }

    /**
     * 获取中转港。
     *
     * @return 中转港
     */
    public String getTransferPort() {
        return transferPort;
    }

    /**
     * 获取卸货港。
     *
     * @return 卸货港
     */
    public String getDischargePort() {
        return dischargePort;
    }

    /**
     * 获取集装箱编号。
     *
     * @return 集装箱编号
     */
    public String getContainerNo() {
        return containerNo;
    }

    /**
     * 获取集装箱尺寸类型。
     *
     * @return 集装箱尺寸类型
     */
    public String getContainerSize() {
        return containerSize;
    }

    /**
     * 获取空重。
     *
     * @return 空重
     */
    public String getEmptyWeight() {
        return emptyWeight;
    }

    /**
     * 获取重控。
     *
     * @return 重控
     */
    public String getZControl() {
        return zControl;
    }

    /**
     * 获取吉控。
     *
     * @return 吉控
     */
    public String getJControl() {
        return jControl;
    }

    /**
     * 获取铅封号。
     *
     * @return 铅封号
     */
    public String getSealNo() {
        return sealNo;
    }

    /**
     * 获取交货条款。
     *
     * @return 交货条款
     */
    public String getDeliveryClause() {
        return deliveryClause;
    }

    /**
     * 获取收货人。
     *
     * @return 收货人
     */
    public String getConsignee() {
        return consignee;
    }

    /**
     * 获取货物名称。
     *
     * @return 货物名称
     */
    public String getCargoName() {
        return cargoName;
    }

    /**
     * 获取件数。
     *
     * @return 件数
     */
    public String getPackageCount() {
        return packageCount;
    }

    /**
     * 获取是否包装。
     *
     * @return 是否包装
     */
    public String getWrap() {
        return wrap;
    }

    /**
     * 获取重量。
     *
     * @return 重量
     */
    public String getWeight() {
        return weight;
    }

    /**
     * 获取体积。
     *
     * @return 体积
     */
    public String getVolume() {
        return volume;
    }

    /**
     * 获取冷藏温度。
     *
     * @return 冷藏温度
     */
    public String getRefrigeratedTemperature() {
        return refrigeratedTemperature;
    }

    /**
     * 获取危险品等级。
     *
     * @return 危险品等级
     */
    public String getDangerousGoodsClass() {
        return dangerousGoodsClass;
    }

    /*-------------------- setter --------------------*/

    /**
     * 设置驳船船名。
     *
     * @param bargeName 驳船船名
     */
    public void setBargeName(String bargeName) {
        this.bargeName = bargeName;
    }

    /**
     * 设置驳船航次。
     *
     * @param bargeVoyage 驳船航次
     */
    public void setBargeVoyage(String bargeVoyage) {
        this.bargeVoyage = bargeVoyage;
    }

    /**
     * 设置集装箱提单票据编号。
     *
     * @param ladeBillNo 集装箱提单票据编号
     */
    public void setLadeBillNo(String ladeBillNo) {
        this.ladeBillNo = ladeBillNo;
    }

    /**
     * 设置装货港。
     *
     * @param loadPort 装货港
     */
    public void setLoadPort(String loadPort) {
        this.loadPort = loadPort;
    }

    /**
     * 设置中转港。
     *
     * @param transferPort 中转港
     */
    public void setTransferPort(String transferPort) {
        this.transferPort = transferPort;
    }

    /**
     * 设置卸货港。
     *
     * @param dischargePort 卸货港
     */
    public void setDischargePort(String dischargePort) {
        this.dischargePort = dischargePort;
    }

    /**
     * 设置集装箱编号。
     *
     * @param containerNo 集装箱编号
     */
    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    /**
     * 设置集装箱尺寸类型。
     *
     * @param containerSize 集装箱尺寸类型
     */
    public void setContainerSize(String containerSize) {
        this.containerSize = containerSize;
    }

    /**
     * 设置空重。
     *
     * @param emptyWeight 空重
     */
    public void setEmptyWeight(String emptyWeight) {
        this.emptyWeight = emptyWeight;
    }

    /**
     * 设置重控。
     *
     * @param zControl 重控
     */
    public void setZControl(String zControl) {
        this.zControl = zControl;
    }

    /**
     * 设置吉控。
     *
     * @param jControl 吉控
     */
    public void setJControl(String jControl) {
        this.jControl = jControl;
    }

    /**
     * 设置铅封号。
     *
     * @param sealNo 铅封号
     */
    public void setSealNo(String sealNo) {
        this.sealNo = sealNo;
    }

    /**
     * 设置交货条款。
     *
     * @param deliveryClause 交货条款
     */
    public void setDeliveryClause(String deliveryClause) {
        this.deliveryClause = deliveryClause;
    }

    /**
     * 设置收货人。
     *
     * @param consignee 收货人
     */
    public void setConsignee(String consignee) {
        this.consignee = consignee;
    }

    /**
     * 设置货物名称。
     *
     * @param cargoName 货物名称
     */
    public void setCargoName(String cargoName) {
        this.cargoName = cargoName;
    }

    /**
     * 设置件数。
     *
     * @param packageCount 件数
     */
    public void setPackageCount(String packageCount) {
        this.packageCount = packageCount;
    }

    /**
     * 设置是否包装。
     *
     * @param wrap 是否包装
     */
    public void setWrap(String wrap) {
        this.wrap = wrap;
    }

    /**
     * 设置重量。
     *
     * @param weight 重量
     */
    public void setWeight(String weight) {
        this.weight = weight;
    }

    /**
     * 设置体积。
     *
     * @param volume 体积
     */
    public void setVolume(String volume) {
        this.volume = volume;
    }

    /**
     * 设置冷藏温度。
     *
     * @param refrigeratedTemperature 冷藏温度
     */
    public void setRefrigeratedTemperature(String refrigeratedTemperature) {
        this.refrigeratedTemperature = refrigeratedTemperature;
    }

    /**
     * 设置危险品等级。
     *
     * @param dangerousGoodsClass 危险品等级
     */
    public void setDangerousGoodsClass(String dangerousGoodsClass) {
        this.dangerousGoodsClass = dangerousGoodsClass;
    }

    /*-------------------- Object methods --------------------*/

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        InputEdiRecord that = (InputEdiRecord) o;
        return Objects.equals(bargeName, that.bargeName) &&
                   Objects.equals(bargeVoyage, that.bargeVoyage) &&
                   Objects.equals(ladeBillNo, that.ladeBillNo) &&
                   Objects.equals(loadPort, that.loadPort) &&
                   Objects.equals(transferPort, that.transferPort) &&
                   Objects.equals(dischargePort, that.dischargePort) &&
                   Objects.equals(containerNo, that.containerNo) &&
                   Objects.equals(containerSize, that.containerSize) &&
                   Objects.equals(emptyWeight, that.emptyWeight) &&
                   Objects.equals(zControl, that.zControl) &&
                   Objects.equals(jControl, that.jControl) &&
                   Objects.equals(sealNo, that.sealNo) &&
                   Objects.equals(deliveryClause, that.deliveryClause) &&
                   Objects.equals(consignee, that.consignee) &&
                   Objects.equals(cargoName, that.cargoName) &&
                   Objects.equals(packageCount, that.packageCount) &&
                   Objects.equals(wrap, that.wrap) &&
                   Objects.equals(weight, that.weight) &&
                   Objects.equals(volume, that.volume) &&
                   Objects.equals(refrigeratedTemperature, that.refrigeratedTemperature) &&
                   Objects.equals(dangerousGoodsClass, that.dangerousGoodsClass);
    }

    @Override
    public int hashCode() {
        return Objects.hash(bargeName, bargeVoyage, ladeBillNo, loadPort, transferPort, dischargePort,
            containerNo, containerSize, emptyWeight, zControl, jControl, sealNo, deliveryClause,
            consignee, cargoName, packageCount, wrap, weight, volume, refrigeratedTemperature,
            dangerousGoodsClass);
    }

    @Override
    public String toString() {
        return "InputEdiRecord{" +
                   "bargeName='" + bargeName + '\'' +
                   ", bargeVoyage='" + bargeVoyage + '\'' +
                   ", ladeBillNo='" + ladeBillNo + '\'' +
                   ", loadPort='" + loadPort + '\'' +
                   ", transferPort='" + transferPort + '\'' +
                   ", dischargePort='" + dischargePort + '\'' +
                   ", containerNo='" + containerNo + '\'' +
                   ", containerSize='" + containerSize + '\'' +
                   ", emptyWeight='" + emptyWeight + '\'' +
                   ", zControl='" + zControl + '\'' +
                   ", jControl='" + jControl + '\'' +
                   ", sealNo='" + sealNo + '\'' +
                   ", deliveryClause='" + deliveryClause + '\'' +
                   ", consignee='" + consignee + '\'' +
                   ", cargoName='" + cargoName + '\'' +
                   ", packageCount='" + packageCount + '\'' +
                   ", wrap='" + wrap + '\'' +
                   ", weight='" + weight + '\'' +
                   ", volume='" + volume + '\'' +
                   ", refrigeratedTemperature='" + refrigeratedTemperature + '\'' +
                   ", dangerousGoodsClass='" + dangerousGoodsClass + '\'' +
                   '}';
    }

}