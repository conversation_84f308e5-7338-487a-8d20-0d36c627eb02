package cn.bluesking.nanfeng.tools.work.common;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.bluesking.nanfeng.tools.common.utils.Utilities;
import cn.bluesking.nanfeng.tools.work.common.annotation.ExcelTitles;

/**
 * <AUTHOR>
 */
class Row2EntityConverter<T> {

    private static final Logger logger = LoggerFactory.getLogger(Row2EntityConverter.class);

    private final Class<T> targetClass;

    private final Map<Integer, BiConsumer<T, Object>> columnIndexToSetterMethod;

    public Row2EntityConverter(Class<T> targetClass, Row titleRow) {

        this.targetClass = targetClass;
        this.columnIndexToSetterMethod = new HashMap<>();
        initColumnIndexToSetterMethod(targetClass, titleRow, this.columnIndexToSetterMethod);
    }

    public boolean isValid() {
        return MapUtils.isNotEmpty(columnIndexToSetterMethod);
    }

    public T convert(Row row) {

        try {

            Constructor<T> constructor = targetClass.getConstructor();
            T instance = constructor.newInstance();

            boolean allCellValueIsEmpty = true;
            for (Map.Entry<Integer, BiConsumer<T, Object>> entry : columnIndexToSetterMethod.entrySet()) {

                Integer columnIndex = entry.getKey();
                BiConsumer<T, Object> setter = entry.getValue();

                String cellValue = getCellStringValue(row.getCell(columnIndex));
                if (StringUtils.isNotEmpty(cellValue)) {
                    setter.accept(instance, cellValue);
                    allCellValueIsEmpty = false;
                }
            }

            return allCellValueIsEmpty ? null : instance;
        }
        catch (Exception e) {
            throw new ExcelDataReaderHelper.DataReaderException("failed to convert Row to Entity!", e);
        }

    }

    private void initColumnIndexToSetterMethod(Class<T> targetClass,
                                               Row titleRow,
                                               Map<Integer, BiConsumer<T, Object>> columnIndexToSetterMethod) {

        ColumnReflection<T> columnReflection = buildColumnReflection(targetClass);

        short firstCellNum = titleRow.getFirstCellNum();
        short lastCellNum = titleRow.getLastCellNum();

        for (int i = firstCellNum; i < lastCellNum; i++) {

            String title = titleRow.getCell(i).getStringCellValue();
            BiConsumer<T, Object> setter = columnReflection.getSetter(title);
            if (Objects.nonNull(setter)) {
                columnIndexToSetterMethod.put(i, setter);
            }
        }
    }

    @SuppressWarnings("java:S3011")
    private ColumnReflection<T> buildColumnReflection(Class<T> targetClass) {

        try {

            ColumnReflection<T> columnReflection = new ColumnReflection<>();

            BeanInfo beanInfo = Introspector.getBeanInfo(targetClass);
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();

            for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {

                String fieldName = getFieldName(propertyDescriptor);
                String[] titles = getTitles(targetClass, fieldName);
                Method writeMethod = propertyDescriptor.getWriteMethod();
                if (ArrayUtils.isNotEmpty(titles) && Objects.nonNull(writeMethod)) {

                    logger.debug("titles[{}] -> {}.{}", Arrays.toString(titles), targetClass.getName(), fieldName);
                    assert titles != null;
                    columnReflection.addRef(titles, (instance, value) -> {

                        writeMethod.setAccessible(true);
                        try {
                            writeMethod.invoke(instance, value);
                        }
                        catch (Exception e) {
                            throw new ExcelDataReaderHelper.DataReaderException(String.format(
                                "failed to invoke Class[%s] field[%s] setter method by instance[%s] and " +
                                    "value[%s]", targetClass.getName(), fieldName, instance, value), e);
                        }
                    });
                }
                else {

                    if (!"class".equals(fieldName)) {
                        logger.warn("Class[{}] field[{}] expect setter method!",
                            targetClass.getName(), fieldName);
                    }
                }
            }

            return columnReflection;
        }
        catch (Exception e) {
            throw new ExcelDataReaderHelper.DataReaderException(String.format(
                "failed to build ColumnReflection by targetClass[%s]", targetClass.getName()), e);
        }

    }

    private String getFieldName(PropertyDescriptor propertyDescriptor) {

        String rawFieldName = propertyDescriptor.getName();
        return Character.isLowerCase(rawFieldName.charAt(0))
                   ? rawFieldName
                   : Utilities.capitalFirstChar(rawFieldName);
    }

    private String[] getTitles(Class<T> targetClass, String fieldName) {

        try {

            Field field = targetClass.getDeclaredField(fieldName);
            ExcelTitles annotation = field.getDeclaredAnnotation(ExcelTitles.class);
            if (Objects.nonNull(annotation)) {

                String[] titles = annotation.value();
                if (ArrayUtils.isNotEmpty(titles)) {
                    return titles;
                }
                else {
                    logger.warn("Class[{}] field[{}] define @ExcelTitles annotation but do not config titles!",
                        targetClass.getName(), fieldName);
                }
            }
        }
        catch (NoSuchFieldException e) {
            // ignore exception
        }

        return new String[0];
    }

    private String getCellStringValue(Cell cell) {

        if (Objects.isNull(cell)) {
            return null;
        }
        else {
            switch(cell.getCellType()) {
                case NUMERIC:
                    Double doubleValue = cell.getNumericCellValue();
                    // 格式化科学计数法，取一位整数。
                    DecimalFormat df = new DecimalFormat("0");
                    return df.format(doubleValue);
                case STRING:
                    return cell.getStringCellValue();
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    return cell.getCellFormula();
                default:
                    return StringUtils.EMPTY;
            }

        }

    }

}