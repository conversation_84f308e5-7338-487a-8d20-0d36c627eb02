package cn.bluesking.nanfeng.tools.common.http;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Objects;
import java.util.Optional;

import cn.bluesking.nanfeng.tools.common.http.ApiClient.ProxyType;

/**
 * HTTP 代理服务器配置类。
 * <p>提供完整的代理服务器配置功能，包括代理地址、端口、认证信息、代理类型等。
 * <p>支持 HTTP 和 SOCKS 两种代理类型，支持用户名密码认证。
 *
 * <p><b>使用示例：</b>
 * <pre>
 * // 创建简单的 HTTP 代理配置
 * HttpProxyConfig config = HttpProxyConfig.http("proxy.example.com", 8080);
 *
 * // 创建带认证的 HTTP 代理配置
 * HttpProxyConfig config = HttpProxyConfig.http("proxy.example.com", 8080, "username", "password");
 *
 * // 创建 SOCKS 代理配置
 * HttpProxyConfig config = HttpProxyConfig.socks("socks-proxy.example.com", 1080);
 *
 * // 创建禁用的代理配置
 * HttpProxyConfig config = HttpProxyConfig.disabled();
 *
 * // 检查代理配置
 * if (config.isEnabled()) {
 *     System.out.println("代理地址：" + config.getAddress());
 *     System.out.println("是否需要认证：" + config.hasAuthentication());
 * }
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-06-18
 * @since 2.1.9
 */
public final class HttpProxyConfig {

    /**
     * 是否启用代理
     */
    private final boolean enabled;

    /**
     * 代理服务器主机地址
     */
    private final String host;

    /**
     * 代理服务器端口
     */
    private final int port;

    /**
     * 代理认证用户名（可选）
     */
    private final String username;

    /**
     * 代理认证密码（可选）
     */
    private final String password;

    /**
     * 代理类型
     */
    private final ProxyType type;

    /*-------------------- constructor --------------------*/

    /**
     * 私有构造函数，使用静态工厂方法创建实例。
     *
     * @param enabled 是否启用代理
     * @param host 代理服务器主机地址
     * @param port 代理服务器端口
     * @param username 代理认证用户名
     * @param password 代理认证密码
     * @param type 代理类型
     */
    private HttpProxyConfig(boolean enabled, String host, int port, String username, String password, ProxyType type) {
        this.enabled = enabled;
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
        this.type = type;
    }

    /*-------------------- public static factory method --------------------*/

    /**
     * 创建 HTTP 代理配置（无认证）
     *
     * @param host 代理服务器主机地址
     * @param port 代理服务器端口
     * @return HTTP 代理配置实例
     * @throws IllegalArgumentException 如果主机地址为空或端口无效
     */
    public static HttpProxyConfig http(String host, int port) {
        validateHostAndPort(host, port);
        return new HttpProxyConfig(true, host, port, null, null, ProxyType.HTTP);
    }

    /**
     * 创建 HTTP 代理配置（带认证）
     *
     * @param host 代理服务器主机地址
     * @param port 代理服务器端口
     * @param username 代理认证用户名
     * @param password 代理认证密码
     * @return HTTP 代理配置实例
     * @throws IllegalArgumentException 如果参数无效
     */
    public static HttpProxyConfig http(String host, int port, String username, String password) {
        validateHostAndPort(host, port);
        validateCredentials(username, password);
        return new HttpProxyConfig(true, host, port, username, password, ProxyType.HTTP);
    }

    /**
     * 创建 SOCKS 代理配置（无认证）
     *
     * @param host 代理服务器主机地址
     * @param port 代理服务器端口
     * @return SOCKS 代理配置实例
     * @throws IllegalArgumentException 如果主机地址为空或端口无效
     */
    public static HttpProxyConfig socks(String host, int port) {
        validateHostAndPort(host, port);
        return new HttpProxyConfig(true, host, port, null, null, ProxyType.SOCKS);
    }

    /**
     * 创建 SOCKS 代理配置（带认证）
     *
     * @param host 代理服务器主机地址
     * @param port 代理服务器端口
     * @param username 代理认证用户名
     * @param password 代理认证密码
     * @return SOCKS 代理配置实例
     * @throws IllegalArgumentException 如果参数无效
     */
    public static HttpProxyConfig socks(String host, int port, String username, String password) {
        validateHostAndPort(host, port);
        validateCredentials(username, password);
        return new HttpProxyConfig(true, host, port, username, password, ProxyType.SOCKS);
    }

    /**
     * 创建指定类型的代理配置（无认证）
     *
     * @param host 代理服务器主机地址
     * @param port 代理服务器端口
     * @param type 代理类型
     * @return 代理配置实例
     * @throws IllegalArgumentException 如果参数无效
     */
    public static HttpProxyConfig of(String host, int port, ProxyType type) {
        validateHostAndPort(host, port);
        if (type == null) {
            throw new IllegalArgumentException("代理类型不能为空");
        }
        return new HttpProxyConfig(true, host, port, null, null, type);
    }

    /**
     * 创建指定类型的代理配置（带认证）
     *
     * @param host 代理服务器主机地址
     * @param port 代理服务器端口
     * @param username 代理认证用户名
     * @param password 代理认证密码
     * @param type 代理类型
     * @return 代理配置实例
     * @throws IllegalArgumentException 如果参数无效
     */
    public static HttpProxyConfig of(String host, int port, String username, String password, ProxyType type) {
        validateHostAndPort(host, port);
        validateCredentials(username, password);
        if (type == null) {
            throw new IllegalArgumentException("代理类型不能为空");
        }
        return new HttpProxyConfig(true, host, port, username, password, type);
    }

    /**
     * 创建禁用的代理配置
     *
     * @return 禁用的代理配置实例
     */
    public static HttpProxyConfig disabled() {
        return new HttpProxyConfig(false, null, 0, null, null, null);
    }

    /*-------------------- getter --------------------*/

    /**
     * 判断代理是否启用
     *
     * @return 如果代理启用返回 true，否则返回 false。
     */
    public boolean isEnabled() {
        return enabled;
    }

    /**
     * 获取代理服务器主机地址
     *
     * @return 代理服务器主机地址，如果代理未启用则返回 null。
     */
    public String getHost() {
        return host;
    }

    /**
     * 获取代理服务器端口
     *
     * @return 代理服务器端口，如果代理未启用则返回 0。
     */
    public int getPort() {
        return port;
    }

    /**
     * 获取代理认证用户名
     *
     * @return 代理认证用户名的 Optional 包装，如果没有设置则返回空 Optional。
     */
    public Optional<String> getUsername() {
        return Optional.ofNullable(username);
    }

    /**
     * 获取代理认证密码
     *
     * @return 代理认证密码的 Optional 包装，如果没有设置则返回空 Optional。
     */
    public Optional<String> getPassword() {
        return Optional.ofNullable(password);
    }

    /**
     * 获取代理类型
     *
     * @return 代理类型，如果代理未启用则返回 null。
     */
    public ProxyType getType() {
        return type;
    }

    /**
     * 获取代理服务器地址（格式：host:port）。
     *
     * @return 代理服务器地址字符串，如果代理未启用则返回 "disabled"。
     */
    public String getAddress() {
        if (!enabled) {
            return "disabled";
        }
        return host + ":" + port;
    }

    /*-------------------- public method --------------------*/

    /**
     * 判断是否配置了认证信息
     *
     * @return 如果配置了用户名和密码返回 true，否则返回 false。
     */
    public boolean hasAuthentication() {
        return username != null && !username.trim().isEmpty()
            && password != null && !password.trim().isEmpty();
    }

    /**
     * 判断代理是否配置了认证凭据（别名方法，与 hasAuthentication 功能相同）。
     *
     * @return 如果配置了认证凭据返回 true，否则返回 false。
     */
    public boolean hasCredentials() {
        return hasAuthentication();
    }

    /**
     * 创建 Java 标准库的 Proxy 对象
     *
     * @return Java Proxy 对象，如果代理未启用则返回 Proxy.NO_PROXY。
     */
    public Proxy toJavaProxy() {
        if (!enabled) {
            return Proxy.NO_PROXY;
        }

        Proxy.Type javaProxyType = (type == ProxyType.SOCKS) ? Proxy.Type.SOCKS : Proxy.Type.HTTP;
        return new Proxy(javaProxyType, new InetSocketAddress(host, port));
    }

    /**
     * 创建新的代理配置，修改代理类型。
     *
     * @param newType 新的代理类型
     * @return 新的代理配置实例
     * @throws IllegalArgumentException 如果新类型为空
     */
    public HttpProxyConfig withType(ProxyType newType) {
        if (newType == null) {
            throw new IllegalArgumentException("代理类型不能为空");
        }
        return new HttpProxyConfig(enabled, host, port, username, password, newType);
    }

    /**
     * 创建新的代理配置，修改认证信息。
     *
     * @param newUsername 新的用户名
     * @param newPassword 新的密码
     * @return 新的代理配置实例
     */
    public HttpProxyConfig withCredentials(String newUsername, String newPassword) {
        return new HttpProxyConfig(enabled, host, port, newUsername, newPassword, type);
    }

    /**
     * 创建新的代理配置，移除认证信息。
     *
     * @return 新的代理配置实例（无认证）
     */
    public HttpProxyConfig withoutCredentials() {
        return new HttpProxyConfig(enabled, host, port, null, null, type);
    }

    /**
     * 验证代理配置是否有效
     *
     * @return 如果配置有效返回 true，否则返回 false。
     */
    public boolean isValid() {
        if (!enabled) {
            return true; // 禁用的代理配置总是有效的
        }

        return host != null && !host.trim().isEmpty()
            && port > 0 && port <= 65535
            && type != null;
    }

    /*-------------------- private static method --------------------*/

    /**
     * 验证主机地址和端口的有效性
     *
     * @param host 主机地址
     * @param port 端口号
     * @throws IllegalArgumentException 如果主机地址为空或端口无效
     */
    private static void validateHostAndPort(String host, int port) {
        if (host == null || host.trim().isEmpty()) {
            throw new IllegalArgumentException("代理服务器主机地址不能为空");
        }
        if (port <= 0 || port > 65535) {
            throw new IllegalArgumentException("代理服务器端口必须在 1-65535 范围内，当前值：" + port);
        }
    }

    /**
     * 验证认证凭据的有效性
     *
     * @param username 用户名
     * @param password 密码
     * @throws IllegalArgumentException 如果用户名或密码为空
     */
    private static void validateCredentials(String username, String password) {
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("代理认证用户名不能为空");
        }
        if (password == null || password.trim().isEmpty()) {
            throw new IllegalArgumentException("代理认证密码不能为空");
        }
    }

    /*-------------------- equals and hashCode --------------------*/

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        HttpProxyConfig that = (HttpProxyConfig) o;
        return enabled == that.enabled
            && port == that.port
            && Objects.equals(host, that.host)
            && Objects.equals(username, that.username)
            && Objects.equals(password, that.password)
            && type == that.type;
    }

    @Override
    public int hashCode() {
        return Objects.hash(enabled, host, port, username, password, type);
    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        if (!enabled) {
            return "HttpProxyConfig{disabled}";
        }
        return "HttpProxyConfig{" + "type=" + type
                        + ", address=" + host + ':' + port
                        + ", hasAuth=" + hasAuthentication()
                        + '}';
    }

}