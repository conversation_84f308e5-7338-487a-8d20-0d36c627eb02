package cn.bluesking.nanfeng.tools.common.utils;

import jakarta.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;

/**
 * 获取 IP 工具类。
 *
 * <AUTHOR>
 */
public final class IpUtils {

    /** 从环境变量 HTTP_IP_HEADER_NAME 中读取 HTTP 请求头的名称，用于读取请求的真实客户端 IP。 */
    private static final String IP_HEADER_NAME = System.getenv("HTTP_IP_HEADER_NAME");

    /*-------------------- constructor --------------------*/

    private IpUtils() {
        throw new IllegalStateException("Utility class");
    }

    /*-------------------- public static method --------------------*/

    /**
     * 不同平台获取请求客户端真实 IP 的规则不尽相同：<ul>。
     *     <li>拼多多真实 IP：<a href=『https://www.tapd.cn/22185411/documents/show/1122185411001001055?file_type=pdf&file_ext=pdf『>request.getHeader(』chiru-real-ip』);</a></li>。
     *     <li>聚石塔 WAF 获取真实 IP（容器 K8s 配置方案）：<a href=『https://help.aliyun.com/document_detail/42205.html?spm=a2c4g.11186623.6.592.121e4942OFjWTi『>request.getHeader(』X-Original-Forwarded-For』);</a></li>。
     *     <li>K8s 中获取真实 IP：request.getHeader(『X-Forwarded-For』);。。</li>
     *     <li>Nginx 反向代理中获取真实 IP：request.getHeader(『X-Real-IP』);。。</li>
     * </ul>
     *
     * <h2>存在风险：客户端 IP 被伪造，导致日志无法记录真实的 IP 地址。</h2>
     * <p>X-Forwarded-For 是一个 HTTP 扩展头部，用来表示 HTTP 请求端真实 IP。但是由于 X-Forwarded-For 可以被任意修改，导致日志中记录的。
     * 客户端 IP 地址可以被伪造。<ul>
     *     <li>对于没有使用 Nginx 反向代理服务器的 Web 应用，必须使用从 TCP 连接中得到的 <b>Remote Address</b>，才是用户真实的 IP。</li>
     *     <li>对于使用 Nginx 反向代理服务器的Web应用，可使用 <b>proxy_set_header X-Real-IP $remote_addr</b> 正确配置 set Headers，。
     *     如果使用了多层代理，那对于最外层代理，可以使用 <b>proxy_set_header X-Forwarded-For $remote_addr</b> 来获取客户端的真实。
     *     IP 地址。</li>
     * </ul>
     */
    public static String getClientIp(HttpServletRequest request) {

        String clientIp = null;
        if (StringUtils.isNotEmpty(IP_HEADER_NAME)) {
            clientIp = request.getHeader(IP_HEADER_NAME);
        }

        if (isInvalidClientIp(clientIp)) {
            // 尝试从请求头获取 X-Forwarded-For
            clientIp = request.getHeader("X-Forwarded-For");
        }

        if (isInvalidClientIp(clientIp)) {
            // 尝试从请求头获取 X-Real-IP
            clientIp = request.getHeader("X-Real-IP");
        }

        if (isInvalidClientIp(clientIp)) {
            // 获取远程地址
            clientIp = request.getRemoteAddr();
        }

        // 如果是多级代理，取第一个 IP 地址。
        if (clientIp != null && clientIp.contains(",")) {
            clientIp = clientIp.split(",")[0].trim();
        }

        // 如果仍然获取不到，使用默认 IP。
        if (clientIp == null || clientIp.isEmpty()) {
            clientIp = "127.0.0.1";
        }

        return clientIp;
    }

    public static boolean isIp(String clientIp) {
        return clientIp != null && (isIpv4(clientIp) || isIpv6(clientIp));
    }

    public static boolean isIpv4(String ip) {
        return ip.matches("^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$");
    }

    public static boolean isIpv6(String ip) {
        return ip.matches("^([\\da-fA-F]{1,4}:){7}[\\da-fA-F]{1,4}$");
    }

    /*-------------------- private static method --------------------*/

    private static boolean isInvalidClientIp(String clientIp) {
        return clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp);
    }

}