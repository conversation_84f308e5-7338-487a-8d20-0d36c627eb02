package cn.bluesking.nanfeng.tools.common.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 字符串工具类，提供字符串处理相关的工具方法。
 * <p>提供一系列字符串操作工具方法，包括特殊字符转义、字符串长度限制、空字符删除、空白字符处理等。
 *
 * <AUTHOR>
 * @date 2024-02-29
 * @since 1.0.0
 */
public final class StringTools {

    /**
     * Unicode 空字符。
     */
    private static final char UTF_0000 = '\u0000';

    /**
     * 常见的不可见字符，包括：回车、换行、制表符、普通空格、不间断空格、零宽不换行空格等。
     */
    private static final String TRIM_CHARS = "\r\n\t \u00A0\uFEFF";

    /**
     * From RFC 7159, 『All Unicode characters may be placed within the
     * quotation marks except for the characters that must be escaped:
     * quotation mark, reverse solidus, and the control characters
     * (U+0000 through U+001F).』
     *
     * <p>We also escape '\u2028' and '\u2029', which JavaScript interprets as
     * newline characters. This prevents eval() from failing with a syntax
     * error.
     *
     * @see http://code.google.com/p/google-gson/issues/detail?id=341
     */
    private static final String[] specialReplacementChars;

    static {

        specialReplacementChars = new String[128];
        // ASCII 码前 31 位是不可见的控制符
        for (int i = 0; i <= 31; i++) {
            specialReplacementChars[i] = String.format("\\u%04x", i);
        }

        specialReplacementChars['"'] = "\\\"";
        specialReplacementChars['\\'] = "\\\\";
        specialReplacementChars['\t'] = "\\t";
        specialReplacementChars['\b'] = "\\b";
        specialReplacementChars['\n'] = "\\n";
        specialReplacementChars['\r'] = "\\r";
        specialReplacementChars['\f'] = "\\f";
    }

    /*-------------------- constructor --------------------*/

    /**
     * 私有构造函数，防止实例化。
     */
    private StringTools() {
        throw new IllegalStateException("Utility class");
    }

    /*-------------------- public static method --------------------*/

    /**
     * 转义特殊字符。
     * escapeSpecialChars(『aaa\b bbb \n』) -> 『aaa\\b bbb \\n』
     *
     * @param source 源字符串
     * @return 转义后的字符串
     * @see com.google.gson.stream.JsonWriter
     */
    public static String escapeSpecialChars(String source) {
        StringBuilder builder = new StringBuilder();
        int last = 0;
        int length = source.length();
        for (int i = 0; i < length; i++) {
            char c = source.charAt(i);
            String replacement;
            if (c < 128) {
                replacement = specialReplacementChars[c];
                if (replacement == null) {
                    continue;
                }

            }

            else if (c == '\u2028') {
                replacement = "\\u2028";
            }

            else if (c == '\u2029') {
                replacement = "\\u2029";
            }

            else {
                continue;
            }

            if (last < i) {
                builder.append(source, last, i);
            }

            builder.append(replacement);
            last = i + 1;
        }

        if (last == 0) {
            return source;
        }

        else if (last < length) {
            builder.append(source, last, length);
        }

        return builder.toString();
    }

    /**
     * 限制字符串长度，如果字符串长度大于指定值时截断。
     *
     * @param str 原始字符串
     * @param len 限制长度
     * @return 处理后的字符串
     */
    public static String limitLength(String str, int len) {
        if (str != null && str.length() > len) {
            return str.substring(0, len);
        }

        return str;
    }

    /**
     * 移除 {@code \\u0000} 字符。
     *
     * @param str 原始字符串
     * @return 处理后的字符串
     */
    public static String removeZeroChar(String str) {
        if (str == null || str.indexOf(UTF_0000) == -1) {
            return str;
        }

        return StringUtils.remove(str, UTF_0000);
    }

    /**
     * 去除半角、全角空格，tab 制表符，换行符。
     *
     * @param str 原始字符串
     * @return 处理后的字符串
     */
    public static String trim(String str) {
        if (str == null) {
            return null;
        }

        int length = str.length();
        int start = 0;

        // 查找字符串开头的空白字符
        while ((start < length) && (TRIM_CHARS.indexOf(str.charAt(start)) != -1)) {
            start ++;
        }

        // 查找字符串结尾的空白字符
        while ((start < length) && (TRIM_CHARS.indexOf(str.charAt(length - 1)) != -1)) {
            length --;
        }

        return ((start > 0) || (length < str.length())) ? str.substring(start, length) : str;
    }

    /**
     * 去掉非中文、非英文并且非数字的字符。
     *
     * @param rawText 原始字符串
     * @return 处理后的字符串
     */
    public static String removeInvalidChar(String rawText) {
        if (rawText == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder();
        char[] chars = rawText.toCharArray();

        for (char c : chars) {
            if (isValidChar(c)) {
                builder.append(c);
            }

        }

        return builder.toString();
    }

    /*-------------------- private static method --------------------*/

    /**
     * 判断字符是否为有效字符（中文、英文或数字）。
     *
     * @param c 要判断的字符
     * @return 是否为有效字符
     */
    private static boolean isValidChar(char c) {
        Character.UnicodeBlock unicodeBlock = Character.UnicodeBlock.of(c);
        return isChinese(unicodeBlock) || isEnglish(unicodeBlock);
    }

    /**
     * 判断 Unicode 块是否为中文字符。
     *
     * @param unicodeBlock Unicode 块
     * @return 是否为中文字符
     */
    private static boolean isChinese(Character.UnicodeBlock unicodeBlock) {
        return (unicodeBlock == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                    || unicodeBlock == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                    || unicodeBlock == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                    || unicodeBlock == Character.UnicodeBlock.GENERAL_PUNCTUATION
                    || unicodeBlock == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                    || unicodeBlock == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS);
    }

    /**
     * 判断 Unicode 块是否为英文字母、数字、英文符号。
     *
     * @param unicodeBlock Unicode 块
     * @return 是否为英文字符
     */
    private static boolean isEnglish(Character.UnicodeBlock unicodeBlock) {
        return (unicodeBlock == Character.UnicodeBlock.BASIC_LATIN);
    }

}