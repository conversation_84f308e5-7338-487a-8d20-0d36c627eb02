package cn.bluesking.nanfeng.tools.common.http;

import java.io.IOException;

import jakarta.annotation.Nonnull;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;
import okhttp3.ResponseBody;
import reactor.core.publisher.MonoSink;

/**
 * OkHttp 响应式回调实现，将 OkHttp 的回调转换为 Reactor 的 Mono。
 * <p>提供线程名称保留和统一的响应处理机制。
 *
 * @param <T> 响应解析后的类型
 * <AUTHOR>
 */
public abstract class OkHttpReactiveCallback<T> implements Callback {

    private static final Logger logger = LoggerFactory.getLogger(OkHttpReactiveCallback.class);

    /**
     * Reactor Mono 的 Sink，用于发出响应或错误。
     */
    protected MonoSink<T> sink;

    /**
     * 调用线程名称，用于错误追踪。
     */
    private final String threadName;

    /**
     * 请求开始时间戳（毫秒）。
     */
    private final long startTimeMills;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数，使用当前线程名称。
     *
     * @param sink Reactor Mono Sink
     */
    protected OkHttpReactiveCallback(MonoSink<T> sink) {
        this(Thread.currentThread().getName(), sink);
    }

    /**
     * 构造函数，指定线程名称。
     *
     * @param threadName 线程名称
     * @param sink Reactor Mono Sink
     */
    protected OkHttpReactiveCallback(String threadName, MonoSink<T> sink) {
        this.threadName = threadName;
        this.sink = sink;
        this.startTimeMills = System.currentTimeMillis();
    }

    /*-------------------- interface method implementation --------------------*/

    @Override
    public void onFailure(@Nonnull Call call, @Nonnull IOException e) {

        String oldThreadName = null;
        try {
            oldThreadName = Thread.currentThread().getName();
            Thread.currentThread().setName(threadName);
        }
        catch (Exception e1) {
            logger.error("failed to set thread name: {}", e1.getMessage(), e1);
        }

        try {
            sink.error(e);
        }

        finally {
            if (oldThreadName != null) {
                Thread.currentThread().setName(oldThreadName);
            }

        }

    }

    @Override
    public void onResponse(@Nonnull Call call, @Nonnull Response response) {

        String oldThreadName = Thread.currentThread().getName();
        ResponseBody responseBody = null;

        try {
            Thread.currentThread().setName(threadName);

            responseBody = response.body();

            if (responseBody == null) {
                sink.error(new IllegalStateException("HTTP response body is empty."));
            }
            else if (response.isSuccessful()) {
                T result = parse(responseBody.string());
                sink.success(result);
            }
            else {
                sink.error(buildUnsuccessfulResponseException(response));
            }

        }
        catch (Exception e) {
            sink.error(e);
        }

        finally {
            if (responseBody != null) {
                try {
                    responseBody.close();
                }
                catch (Exception e) {
                    logger.error("failed to close response body: {}", e.getMessage(), e);
                }

            }

            if (oldThreadName != null) {
                Thread.currentThread().setName(oldThreadName);
            }

        }

    }

    /*-------------------- public method --------------------*/

    /**
     * 获取请求执行耗时（毫秒）。
     *
     * @return 耗时毫秒数
     */
    public long getUsedTimeMills() {
        return System.currentTimeMillis() - startTimeMills;
    }

    /*-------------------- abstract method --------------------*/

    /**
     * 构建非成功响应的异常。
     * 当 HTTP 请求返回非成功状态码（非 200-299）时，调用此方法构建适当的异常。
     *
     * @param response HTTP 响应
     * @return 构建的异常
     */
    public abstract Exception buildUnsuccessfulResponseException(Response response);

    /**
     * 解析响应体文本。
     *
     * @param responseBodyStr 响应体文本
     * @return 解析后的对象
     * @throws Exception 如果解析失败
     */
    public abstract T parse(String responseBodyStr) throws Exception;
}