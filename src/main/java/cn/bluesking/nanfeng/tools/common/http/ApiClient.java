package cn.bluesking.nanfeng.tools.common.http;

import java.util.Map;
import java.util.function.Consumer;

import reactor.core.publisher.Mono;

/**
 * HTTP API 客户端接口，提供响应式的 HTTP 请求操作。
 *
 * <p>这是一个基于 Reactor 的响应式 HTTP 客户端接口，支持所有标准 HTTP 方法，。
 * 并提供链式和函数式的请求配置方式。客户端支持代理配置、请求头定制、。
 * 请求体格式化、响应解析等功能。
 *
 * <h3>核心特性</h3>
 * <ul>
 *   <li><b>响应式编程</b>：基于 Project Reactor，返回 {@link Mono} 类型。</li>
 *   <li><b>链式 API</b>：提供流畅的链式调用接口。</li>
 *   <li><b>代理支持</b>：支持 HTTP 和 SOCKS 代理，包括认证。</li>
 *   <li><b>灵活配置</b>：支持请求头、超时、重试等配置。</li>
 *   <li><b>多种格式</b>：支持 JSON、表单、文本等多种请求和响应格式。</li>
 *   <li><b>错误处理</b>：提供详细的错误信息和异常处理。</li>
 * </ul>
 *
 * <h3>基本使用示例</h3>
 * <pre>
 * // 创建客户端
 * ApiClient client = ApiClientFactory.create();
 *
 * // 简单 GET 请求
 * Mono&lt;User[]&gt; users = client.request()
 *     .uri("/api/users")
 *     .get()
 *     .retrieveJson(User[].class);
 *
 * users.subscribe(userArray ->
 *     System.out.println("用户数量：" + userArray.length));
 *
 * // POST 请求发送 JSON 数据
 * User newUser = new User("张三", "<EMAIL>");
 * Mono&lt;User&gt; createdUser = client.request()
 *     .uri("/api/users")
 *     .post()
 *     .bodyJson(newUser)
 *     .header("Authorization", "Bearer " + token)
 *     .retrieveJson(User.class);
 *
 * createdUser.subscribe(
 *     user -> System.out.println("创建成功：" + user.getName()),
 *     error -> System.err.println("创建失败：" + error.getMessage())
 * );
 *
 * // 链式调用的强大功能
 * client.request()
 *     .uri("/api/secure-data")
 *     .proxy("proxy.company.com", 8080, "username", "password")
 *     .get()
 *     .header("Authorization", "Bearer " + token)
 *     .header("Accept", "application/json")
 *     .disableLogging()  // 敏感数据不记录日志
 *     .retrieveJson(SecureData.class)
 *     .timeout(Duration.ofSeconds(30))
 *     .retry(3)
 *     .subscribe(
 *         data -> processSecureData(data),
 *         error -> handleError(error)
 *     );
 * </pre>
 *
 * <h3>代理功能使用示例</h3>
 * <pre>
 * // 1. 使用简单 HTTP 代理
 * client.request()
 *       .uri("{@code https://api.example.com/data}")
 *       .proxy("proxy.example.com", 8080)
 *       .get()
 *       .retrieveJson(String.class);
 *
 * // 2. 使用带认证的 HTTP 代理
 * client.request()
 *       .uri("{@code https://api.example.com/secure}")
 *       .proxy("proxy.example.com", 8080, "username", "password")
 *       .get()
 *       .header("Authorization", "Bearer token123")
 *       .retrieveJson(String.class);
 *
 * // 3. 使用 SOCKS 代理
 * client.request()
 *       .uri("{@code https://api.example.com/data}")
 *       .proxy("socks-proxy.example.com", 1080)
 *       .proxyType(ProxyType.SOCKS)
 *       .get()
 *       .retrieveJson(String.class);
 *
 * // 4. 显式禁用代理（即使配置了默认代理）
 * client.request()
 *       .uri("{@code https://local-api.example.com/data}")
 *       .noProxy()
 *       .get()
 *       .retrieveJson(String.class);
 * </pre>
 *
 * <h3>高级功能示例</h3>
 * <pre>
 * // 表单数据提交
 * client.request()
 *       .uri("/api/login")
 *       .post()
 *       .formData(params -> {
 *           params.put("username", "admin");
 *           params.put("password", "secret");
 *       })
 *       .retrieveJson(LoginResponse.class);
 *
 * // 禁用日志记录（敏感请求）
 * client.request()
 *       .uri("/api/sensitive-data")
 *       .get()
 *       .disableLogging()
 *       .retrieveJson(SensitiveData.class);
 *
 * // 自定义响应解析
 * client.request()
 *       .uri("/api/custom")
 *       .get()
 *       .retrieve(response -> {
 *           // 自定义解析逻辑
 *           return parseCustomFormat(response);
 *       });
 * </pre>
 *
 * <AUTHOR>
 * @date 2024-02-29
 * @since 1.0.0
 * @see ApiClientFactory
 * @see ApiClientConfig
 * @see HttpProxyConfig
 * @see ResponseParser
 */
public interface ApiClient {

    /*-------------------- 主要 API 方法 --------------------*/

    /**
     * 创建请求构建器，开始构建一个新的 HTTP 请求。
     *
     * <p>这是构建 HTTP 请求的入口点，返回的构建器支持链式调用，可以配置请求方法、URL、请求头、请求体、代理等各种参数。
     *
     * <p><b>使用示例：</b>
     * <pre>
     * // 基本用法
     * client.request()
     *       .uri("/api/users")
     *       .get()
     *       .retrieveJson(User[].class);
     *
     * // 复杂配置
     * client.request()
     *       .uri("/api/data")
     *       .proxy("proxy.example.com", 8080)
     *       .post()
     *       .bodyJson(requestData)
     *       .header("Authorization", "Bearer " + token)
     *       .disableLogging()
     *       .retrieveJson(ResponseData.class);
     * </pre>
     *
     * @return 请求构建器实例，用于配置和发送请求。
     * @see RequestBuilder
     */
    RequestBuilder request();



    /*-------------------- inner enum class --------------------*/

    /**
     * 代理类型枚举，定义支持的代理协议类型。
     *
     * <p>客户端支持两种主要的代理协议：HTTP 代理和 SOCKS 代理，不同的代理类型适用于不同的网络环境和安全要求。
     */
    enum ProxyType {
        /**
         * HTTP 代理（HTTP/HTTPS）。
         *
         * <p>最常见的代理类型，工作在应用层，支持 HTTP 和 HTTPS 协议，代理服务器可以理解 HTTP 协议，能够进行缓存、过滤等操作。
         *
         * <p><b>适用场景：</b>
         * <ul>
         *   <li>企业网络环境中的 Web 访问代理。</li>
         *   <li>需要 HTTP 协议级别功能的场景。</li>
         *   <li>支持认证的代理服务器。</li>
         * </ul>
         */
        HTTP,

        /**
         * SOCKS 代理（SOCKS4/SOCKS5）。
         *
         * <p>工作在会话层的代理协议，不关心应用层协议的具体内容，只是简单地转发数据包。支持 TCP 和 UDP 协议。
         *
         * <p><b>适用场景：</b>
         * <ul>
         *   <li>需要代理非 HTTP 协议的场景。</li>
         *   <li>要求更高匿名性的网络访问。</li>
         *   <li>绕过网络限制的通用代理。</li>
         * </ul>
         */
        SOCKS

    }

    /*-------------------- inner class --------------------*/

    /**
     * HTTP 请求构建器接口，提供流式 API 来构建和发送 HTTP 请求。
     *
     * <p>这是构建 HTTP 请求的核心接口，支持链式调用，可以配置请求的各个方面：
     * <ul>
     *   <li>请求 URL 和路径。</li>
     *   <li>HTTP 方法（GET、POST、PUT 等）。</li>
     *   <li>代理配置。</li>
     *   <li>请求头和请求体。</li>
     *   <li>响应处理。</li>
     * </ul>
     *
     * <p><b>使用模式：</b>
     * <pre>
     * client.request()           // 创建构建器
     *       .uri("/api/users")    // 设置 URL
     *       .proxy(...)           // 配置代理（可选）
     *       .get()                // 选择 HTTP 方法
     *       .header(...)          // 添加请求头
     *       .retrieve(...);       // 发送请求并处理响应
     * </pre>
     */
    interface RequestBuilder {

        /**
         * 设置请求的 URI 路径或完整 URL。
         *
         * <p>如果客户端配置了 baseUrl，则此参数会被视为相对路径，自动拼接到 baseUrl 之后。如果没有配置 baseUrl，则必须提供完整的 URL，
         * 必须以 "http://" 或 "https://" 开头，则被视为完整的 URL。
         *
         * <p><b>示例：</b>
         * <pre>
         * // 相对路径（需要配置 baseUrl）
         * .uri("/api/users")
         * .uri("users/123")
         *
         * // 完整 URL
         * .uri("{@code https://api.example.com/users}")
         * </pre>
         *
         * @param uri 请求路径或完整 URL，不能为 null 或空字符串。
         * @return 请求构建器实例，支持链式调用。
         * @throws IllegalArgumentException 如果 uri 为 null 或空字符串
         */
        RequestBuilder uri(String uri);

        /*-------------------- HTTP 方法配置 --------------------*/

        /**
         * 配置 GET 请求。
         *
         * <p>GET 方法用于请求指定资源的表示，是最常用的 HTTP 方法，GET 请求应该是安全的（不会修改服务器状态）和幂等的。
         *
         * <p><b>特点：</b>
         * <ul>
         *   <li>不包含请求体。</li>
         *   <li>参数通过 URL 查询字符串传递。</li>
         *   <li>可以被缓存。</li>
         *   <li>可以被书签保存。</li>
         * </ul>
         *
         * @return 请求头构建器，用于配置请求头和发送请求。
         */
        HeadersBuilder get();

        /**
         * 配置 POST 请求。
         *
         * <p>POST 方法用于向服务器提交数据，通常用于创建新资源或提交表单，POST 请求不是幂等的，多次执行可能产生不同的结果。
         *
         * <p><b>特点：</b>
         * <ul>
         *   <li>包含请求体。</li>
         *   <li>数据在请求体中传输，更安全。</li>
         *   <li>支持大量数据传输。</li>
         *   <li>不会被缓存。</li>
         * </ul>
         *
         * @return 请求体构建器，用于配置请求体、请求头和发送请求。
         */
        BodyBuilder post();

        /**
         * 配置 PUT 请求。
         *
         * <p>PUT 方法用于创建或完全替换指定资源。PUT 请求是幂等的，多次执行相同的 PUT 请求应该产生相同的结果。
         *
         * <p><b>特点：</b>
         * <ul>
         *   <li>包含请求体。</li>
         *   <li>用于完整更新资源。</li>
         *   <li>幂等操作。</li>
         *   <li>可以创建或更新资源。</li>
         * </ul>
         *
         * @return 请求体构建器，用于配置请求体、请求头和发送请求。
         */
        BodyBuilder put();

        /**
         * 配置 DELETE 请求。
         *
         * <p>DELETE 方法用于删除指定的资源。DELETE 请求是幂等的，删除不存在的资源通常返回成功状态。
         *
         * <p><b>特点：</b>
         * <ul>
         *   <li>通常不包含请求体。</li>
         *   <li>用于删除资源。</li>
         *   <li>幂等操作。</li>
         *   <li>删除后资源不再可用。</li>
         * </ul>
         *
         * @return 请求头构建器，用于配置请求头和发送请求。
         */
        HeadersBuilder delete();

        /**
         * 配置 PATCH 请求。
         *
         * <p>PATCH 方法用于对资源进行部分修改。与 PUT 不同，PATCH 只更新指定的字段，而不是替换整个资源。
         *
         * <p><b>特点：</b>
         * <ul>
         *   <li>包含请求体。</li>
         *   <li>用于部分更新资源。</li>
         *   <li>不一定是幂等的。</li>
         *   <li>更节省带宽。</li>
         * </ul>
         *
         * @return 请求体构建器，用于配置请求体、请求头和发送请求。
         */
        BodyBuilder patch();

        /**
         * 配置 HEAD 请求。
         *
         * <p>HEAD 方法与 GET 方法相同，但服务器只返回响应头，不返回响应体。通常用于获取资源的元信息。
         *
         * <p><b>特点：</b>
         * <ul>
         *   <li>不包含请求体。</li>
         *   <li>响应不包含响应体。</li>
         *   <li>用于获取元信息。</li>
         *   <li>检查资源是否存在。</li>
         * </ul>
         *
         * @return 请求头构建器，用于配置请求头和发送请求。
         */
        HeadersBuilder head();

        /**
         * 配置 OPTIONS 请求。
         *
         * <p>OPTIONS 方法用于获取目标资源所支持的通信选项，常用于 CORS 预检请求，检查服务器支持的方法和头部。
         *
         * <p><b>特点：</b>
         * <ul>
         *   <li>通常不包含请求体。</li>
         *   <li>用于获取服务器能力。</li>
         *   <li>CORS 预检请求。</li>
         *   <li>安全的方法。</li>
         * </ul>
         *
         * @return 请求头构建器，用于配置请求头和发送请求。
         */
        HeadersBuilder options();

        /*-------------------- 代理配置 --------------------*/

        /**
         * 为当前请求指定代理服务器（无认证）。
         *
         * <p>设置代理服务器后，此请求将通过指定的代理服务器发送，默认使用 HTTP 代理类型，如需使用 SOCKS 代理，请调用
         * {@link #proxyType(ProxyType)}。
         *
         * <p><b>示例：</b>
         * <pre>
         * // HTTP 代理
         * client.request()
         *       .uri("/api/data")
         *       .proxy("proxy.company.com", 8080)
         *       .get()
         *       .retrieveJson(String.class);
         *
         * // SOCKS 代理
         * client.request()
         *       .uri("/api/data")
         *       .proxy("socks.company.com", 1080)
         *       .proxyType(ProxyType.SOCKS)
         *       .get()
         *       .retrieveJson(String.class);
         * </pre>
         *
         * @param host 代理服务器主机地址，不能为 null 或空字符串。
         * @param port 代理服务器端口，必须在 1-65535 范围内。
         * @return 请求构建器实例，支持链式调用。
         * @throws IllegalArgumentException 如果 host 为空或 port 无效
         * @see #proxyType(ProxyType)
         * @see #noProxy()
         */
        RequestBuilder proxy(String host, int port);

        /**
         * 为当前请求指定代理服务器（带认证）。
         *
         * <p>设置需要用户名密码认证的代理服务器，支持 HTTP 基本认证，认证信息会安全地传输给代理服务器。
         *
         * <p><b>安全提示：</b>
         * <ul>
         *   <li>认证信息会以 Base64 编码传输，建议使用 HTTPS 代理。</li>
         *   <li>避免在日志中记录认证信息。</li>
         *   <li>定期更换代理认证凭据。</li>
         * </ul>
         *
         * <p><b>示例：</b>
         * <pre>
         * client.request()
         *       .uri("/api/secure-data")
         *       .proxy("secure-proxy.company.com", 8080, "username", "password")
         *       .get()
         *       .retrieveJson(String.class);
         * </pre>
         *
         * @param host 代理服务器主机地址，不能为 null 或空字符串。
         * @param port 代理服务器端口，必须在 1-65535 范围内。
         * @param username 代理认证用户名，不能为 null 或空字符串。
         * @param password 代理认证密码，不能为 null 或空字符串。
         * @return 请求构建器实例，支持链式调用。
         * @throws IllegalArgumentException 如果任何参数为空或无效
         * @see #proxy(String, int)
         * @see #proxyType(ProxyType)
         */
        RequestBuilder proxy(String host, int port, String username, String password);

        /**
         * 指定代理类型。
         *
         * <p>必须在调用 {@link #proxy(String, int)} 或 {@link #proxy(String, int, String, String)} 之后调用此方法。
         * 如果不调用此方法，默认使用 HTTP 代理类型。
         *
         * <p><b>代理类型说明：</b>
         * <ul>
         *   <li><b>HTTP</b>：适用于 HTTP/HTTPS 协议，支持认证。</li>
         *   <li><b>SOCKS</b>：通用代理协议，支持更多协议类型。</li>
         * </ul>
         *
         * @param type 代理类型，不能为 null。
         * @return 请求构建器实例，支持链式调用。
         * @throws IllegalArgumentException 如果 type 为 null
         * @throws IllegalStateException 如果尚未配置代理服务器
         * @see ProxyType
         * @see #proxy(String, int)
         */
        RequestBuilder proxyType(ProxyType type);

        /**
         * 显式指定不使用代理直接连接。
         *
         * <p>即使客户端配置了默认代理，调用此方法也会强制当前请求。绕过代理直接连接到目标服务器。这对于访问内网资源或需要直连的特殊场景很有用。
         *
         * <p><b>使用场景：</b>
         * <ul>
         *   <li>访问内网或本地服务。</li>
         *   <li>绕过代理的性能敏感请求。</li>
         *   <li>代理服务器不可用时的备用方案。</li>
         * </ul>
         *
         * <p><b>示例：</b>
         * <pre>
         * // 即使配置了默认代理，也直接连接。
         * client.request()
         *       .uri("{@code http://localhost:8080/health}")
         *       .noProxy()
         *       .get()
         *       .retrieveJson(HealthStatus.class);
         * </pre>
         *
         * @return 请求构建器实例，支持链式调用。
         * @see ApiClientConfig#setDefaultProxyConfig(HttpProxyConfig)
         */
        RequestBuilder noProxy();

    }

    /*-------------------- 请求头构建器接口 --------------------*/

    /**
     * 请求头构建器接口，用于配置请求头和发送请求。
     *
     * <p>此接口提供了配置 HTTP 请求头的方法，以及最终发送请求并处理响应的方法。
     * <p>支持单个请求头设置、批量请求头设置、函数式配置，以及一些特殊配置如禁用日志等。
     *
     * <p><b>常用请求头示例：</b>
     * <pre>
     * builder.header("Authorization", "Bearer " + token)
     *        .header("Content-Type", "application/json")
     *        .header("User-Agent", "MyApp/1.0")
     *        .header("Accept", "application/json");
     * </pre>
     */
    interface HeadersBuilder {

        /**
         * 添加单个请求头。
         *
         * <p>如果请求头已存在，此方法会覆盖之前的值。请求头名称不区分大小写，但建议使用标准的大小写格式（如 "Content-Type" 而不是
         * "content-type"）。
         *
         * <p><b>常用请求头：</b>
         * <ul>
         *   <li><b>Authorization</b>：认证信息，如『Bearer token』或『Basic base64』。</li>
         *   <li><b>Content-Type</b>：请求体的媒体类型。</li>
         *   <li><b>Accept</b>：客户端能够接收的响应媒体类型。</li>
         *   <li><b>User-Agent</b>：客户端信息。</li>
         *   <li><b>X-Request-ID</b>：请求追踪 ID。</li>
         * </ul>
         *
         * @param name 请求头名称，不能为 null 或空字符串。
         * @param value 请求头值，可以为 null（将移除该请求头）。
         * @return 请求头构建器实例，支持链式调用。
         * @throws IllegalArgumentException 如果 name 为 null 或空字符串
         */
        HeadersBuilder header(String name, String value);

        /**
         * 批量添加请求头。
         *
         * <p>将映射中的所有键值对添加为请求头。如果某个请求头已存在，会被新值覆盖。此方法等价于对映射中的每个条目调用
         * {@link #header(String, String)}。
         *
         * <p><b>示例：</b>
         * <pre>
         * Map&lt;String, String&gt; headers = new HashMap&lt;&gt;();
         * headers.put("Authorization", "Bearer " + token);
         * headers.put("Content-Type", "application/json");
         * headers.put("Accept", "application/json");
         *
         * builder.headers(headers);
         * </pre>
         *
         * @param headers 请求头映射，可以为 null 或空（不会添加任何请求头）。
         * @return 请求头构建器实例，支持链式调用。
         */
        HeadersBuilder headers(Map<String, String> headers);

        /**
         * 使用函数式方式配置请求头。
         *
         * <p>提供一个空的 Map 给消费者函数，允许以函数式的方式配置请求头，这种方式在需要条件性添加请求头时特别有用。
         *
         * <p><b>示例：</b>
         * <pre>
         * builder.headers(headers -> {
         *     headers.put("Content-Type", "application/json");
         *     if (token != null) {
         *         headers.put("Authorization", "Bearer " + token);
         *     }
         *     if (requestId != null) {
         *         headers.put("X-Request-ID", requestId);
         *     }
         * });
         * </pre>
         *
         * @param headersConsumer 请求头配置函数，不能为 null。
         * @return 请求头构建器实例，支持链式调用。
         * @throws IllegalArgumentException 如果 headersConsumer 为 null
         */
        HeadersBuilder headers(Consumer<Map<String, String>> headersConsumer);

        /**
         * 禁用此请求的日志记录。
         *
         * <p>对于包含敏感信息的请求（如认证、支付等），可以调用此方法，禁用日志记录，避免敏感信息泄露到日志文件中。
         *
         * <p><b>适用场景：</b>
         * <ul>
         *   <li>包含密码、令牌等认证信息的请求。</li>
         *   <li>包含个人隐私数据的请求。</li>
         *   <li>包含商业敏感信息的请求。</li>
         *   <li>高频请求（减少日志量）。</li>
         * </ul>
         *
         * <p><b>示例：</b>
         * <pre>
         * client.request()
         *       .uri("/api/login")
         *       .post()
         *       .bodyJson(loginRequest)
         *       .disableLogging()  // 禁用日志，保护密码信息。
         *       .retrieveJson(LoginResponse.class);
         * </pre>
         *
         * @return 请求头构建器实例，支持链式调用。
         */
        HeadersBuilder disableLogging();

        /**
         * 发送请求并使用指定的解析器处理响应。
         *
         * <p>这是最灵活的响应处理方法，允许自定义响应解析逻辑，解析器会接收到完整的响应对象，可以访问状态码、响应头和响应体。
         *
         * <p><b>示例：</b>
         * <pre>
         * // 自定义解析逻辑
         * client.request()
         *       .uri("/api/data")
         *       .get()
         *       .retrieve(response -> {
         *           if (response.getStatusCode() == 200) {
         *               return parseCustomFormat(response.getBody());
         *           } else {
         *               throw new ApiException("请求失败：" + response.getStatusCode());
         *           }
         *       });
         * </pre>
         *
         * @param parser 响应解析器，不能为 null。
         * @param <T> 返回对象类型
         * @return 包含解析后结果的 Mono
         * @throws IllegalArgumentException 如果 parser 为 null
         * @see ResponseParser
         */
        <T> Mono<T> retrieve(ResponseParser<T> parser);

        /**
         * 发送请求并将响应解析为 JSON 对象。
         *
         * <p>这是处理 JSON 响应的便捷方法，会自动将响应体解析为指定的 Java 对象。
         * <p>要求响应的 Content-Type 为 application/json 或兼容的类型。
         *
         * <p><b>支持的类型：</b>
         * <ul>
         *   <li>简单对象：User.class、Product.class 等。</li>
         *   <li>集合类型：User[].class、List&lt;User&gt;.class 等。</li>
         *   <li>泛型类型：需要使用 TypeReference。</li>
         *   <li>基本类型：String.class、Integer.class 等。</li>
         * </ul>
         *
         * <p><b>示例：</b>
         * <pre>
         * // 解析为对象
         * Mono&lt;User&gt; user = client.request()
         *                          .uri("/api/users/123")
         *                          .get()
         *                          .retrieveJson(User.class);
         *
         * // 解析为数组
         * Mono&lt;User[]&gt; users = client.request()
         *                           .uri("/api/users")
         *                           .get()
         *                           .retrieveJson(User[].class);
         * </pre>
         *
         * @param responseClass 目标类型的 Class 对象，不能为 null。
         * @param <T> 返回对象类型
         * @return 包含解析后结果的 Mono
         * @throws IllegalArgumentException 如果 responseClass 为 null
         */
        <T> Mono<T> retrieveJson(Class<T> responseClass);
    }

    /*-------------------- 请求体构建器接口 --------------------*/

    /**
     * 请求体构建器接口，用于配置请求体和请求头。
     *
     * <p>此接口继承自 {@link HeadersBuilder}，除了支持所有请求头配置功能外，还提供了多种设置请求体的方法，包括字符串、JSON 对象、表单数据等。
     *
     * <p><b>支持的请求体格式：</b>
     * <ul>
     *   <li><b>字符串</b>：直接设置字符串内容。</li>
     *   <li><b>JSON 对象</b>：自动序列化 Java 对象为 JSON。</li>
     *   <li><b>表单数据</b>：application/x-www-form-urlencoded 格式。</li>
     * </ul>
     *
     * <p><b>使用示例：</b>
     * <pre>
     * // JSON 请求体
     * client.request()
     *       .uri("/api/users")
     *       .post()
     *       .bodyJson(new User("张三", "<EMAIL>"))
     *       .header("Authorization", "Bearer " + token)
     *       .retrieveJson(User.class);
     *
     * // 表单数据
     * client.request()
     *       .uri("/api/login")
     *       .post()
     *       .formData(params -> {
     *           params.put("username", "admin");
     *           params.put("password", "secret");
     *       })
     *       .retrieveJson(LoginResponse.class);
     * </pre>
     */
    interface BodyBuilder extends HeadersBuilder {

        /**
         * 设置字符串请求体。
         *
         * <p>直接设置请求体的字符串内容。需要手动设置正确的 Content-Type 请求头，这是最灵活的方式，可以发送任何格式的文本数据。
         *
         * <p><b>常用场景：</b>
         * <ul>
         *   <li>发送 XML 数据。</li>
         *   <li>发送纯文本内容。</li>
         *   <li>发送自定义格式的数据。</li>
         *   <li>发送预先序列化的 JSON 字符串。</li>
         * </ul>
         *
         * <p><b>示例：</b>
         * <pre>
         * // 发送 XML 数据
         * String xmlData = "&lt;user&gt;&lt;name&gt;张三&lt;/name&gt;&lt;/user&gt;";
         * client.request()
         *       .uri("/api/users")
         *       .post()
         *       .body(xmlData)
         *       .header("Content-Type", "application/xml")
         *       .retrieveString();
         *
         * // 发送 JSON 字符串
         * String jsonData = "{\"name\":\"张三\",\"email\":\"<EMAIL>\"}";
         * client.request()
         *       .uri("/api/users")
         *       .post()
         *       .body(jsonData)
         *       .header("Content-Type", "application/json")
         *       .retrieveJson(User.class);
         * </pre>
         *
         * @param body 请求体字符串，可以为 null（表示空请求体）。
         * @return 请求头构建器实例，支持链式调用。
         */
        HeadersBuilder body(String body);

        /**
         * 设置 JSON 请求体。
         *
         * <p>将 Java 对象自动序列化为 JSON 格式并设置为请求体，会自动设置 Content-Type 为 application/json。
         *
         * <p><b>支持的对象类型：</b>
         * <ul>
         *   <li>POJO 对象：会序列化所有公共字段和 getter 方法。</li>
         *   <li>集合类型：List、Set、Array 等。</li>
         *   <li>Map 类型：会序列化为 JSON 对象。</li>
         *   <li>基本类型：String、Number、Boolean 等。</li>
         * </ul>
         *
         * <p><b>序列化配置：</b>
         * <ul>
         *   <li>null 值默认会被忽略。</li>
         *   <li>日期格式使用 ISO-8601 标准。</li>
         *   <li>支持 Jackson 注解进行自定义配置。</li>
         * </ul>
         *
         * <p><b>示例：</b>
         * <pre>
         * // 发送用户对象
         * User user = new User("张三", "<EMAIL>");
         * client.request()
         *       .uri("/api/users")
         *       .post()
         *       .bodyJson(user)
         *       .retrieveJson(User.class);
         *
         * // 发送 Map 数据
         * Map&lt;String, Object&gt; data = new HashMap&lt;&gt;();
         * data.put("name", "张三");
         * data.put("age", 30);
         * client.request()
         *       .uri("/api/data")
         *       .post()
         *       .bodyJson(data)
         *       .retrieveString();
         * </pre>
         *
         * @param body 要序列化为 JSON 的对象，可以为 null（表示空 JSON）。
         * @return 请求头构建器实例，支持链式调用。
         */
        HeadersBuilder bodyJson(Object body);

        /**
         * 设置表单数据请求体。
         *
         * <p>将参数映射编码为 application/x-www-form-urlencoded 格式，会自动设置正确的 Content-Type 和进行 URL 编码。
         *
         * <p><b>编码规则：</b>
         * <ul>
         *   <li>参数名和参数值都会进行 URL 编码。</li>
         *   <li>使用 UTF-8 字符集。</li>
         *   <li>参数之间用 & 分隔。</li>
         *   <li>参数名和值之间用 = 分隔。</li>
         * </ul>
         *
         * <p><b>示例：</b>
         * <pre>
         * Map&lt;String, String&gt; params = new HashMap&lt;&gt;();
         * params.put("username", "admin");
         * params.put("password", "secret123");
         * params.put("remember", "true");
         *
         * client.request()
         *       .uri("/api/login")
         *       .post()
         *       .formData(params)
         *       .retrieveJson(LoginResponse.class);
         * </pre>
         *
         * @param params 表单参数映射，可以为 null 或空（表示空表单）。
         * @return 请求头构建器实例，支持链式调用。
         */
        HeadersBuilder formData(Map<String, String> params);

        /**
         * 使用函数式方式设置表单数据。
         *
         * <p>提供一个空的 Map 给消费者函数，允许以函数式的方式配置表单参数，这种方式在需要条件性添加参数时特别有用。
         *
         * <p><b>示例：</b>
         * <pre>
         * client.request()
         *       .uri("/api/login")
         *       .post()
         *       .formData(params -> {
         *           params.put("username", username);
         *           params.put("password", password);
         *           if (rememberMe) {
         *               params.put("remember", "true");
         *           }
         *           if (captcha != null) {
         *               params.put("captcha", captcha);
         *           }
         *       })
         *       .retrieveJson(LoginResponse.class);
         * </pre>
         *
         * @param paramsConsumer 表单参数配置函数，不能为 null。
         * @return 请求头构建器实例，支持链式调用。
         * @throws IllegalArgumentException 如果 paramsConsumer 为 null
         */
        HeadersBuilder formData(Consumer<Map<String, String>> paramsConsumer);

    }

}