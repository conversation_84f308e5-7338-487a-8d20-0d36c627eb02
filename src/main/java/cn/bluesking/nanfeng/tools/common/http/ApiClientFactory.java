package cn.bluesking.nanfeng.tools.common.http;

/**
 * API 客户端工厂类，负责创建 API 客户端实例。
 * <p>支持默认配置和自定义配置。
 *
 * <AUTHOR>
 */
public class ApiClientFactory {

    /**
     * 默认配置的 API 客户端实例。
     */
    private static final ApiClient DEFAULT_CLIENT = buildClient(new ApiClientConfig());

    /**
     * 私有构造函数，防止实例化。
     */
    private ApiClientFactory() {}

    /*-------------------- public static factory method --------------------*/

    /**
     * 创建默认配置的 API 客户端。
     *
     * @return API 客户端
     */
    public static ApiClient create() {
        return DEFAULT_CLIENT;
    }

    /**
     * 使用指定的基础 URL 创建 API 客户端。
     *
     * @param baseUrl 基础 URL
     * @return API 客户端
     */
    public static ApiClient create(String baseUrl) {
        ApiClientConfig config = new ApiClientConfig()
                .setBaseUrl(baseUrl);
        return buildClient(config);
    }

    /**
     * 使用自定义配置创建 API 客户端。
     *
     * @param config API 客户端配置
     * @return API 客户端
     */
    public static ApiClient create(ApiClientConfig config) {
        return buildClient(config);
    }

    /**
     * 创建 API 客户端构建器。
     *
     * @return API 客户端构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /*-------------------- private static method --------------------*/

    /**
     * 根据配置构建 API 客户端实例。
     *
     * @param config API 客户端配置
     * @return API 客户端实例
     */
    private static ApiClient buildClient(ApiClientConfig config) {
        return new OkApiClient(config);
    }

    /*-------------------- inner class --------------------*/

    /**
     * API 客户端构建器，支持链式配置。
     */
    public static class Builder {

        private final ApiClientConfig config = new ApiClientConfig();

        /*-------------------- public method --------------------*/

        /**
         * 设置基础 URL。
         *
         * @param baseUrl 基础 URL
         * @return 构建器
         */
        public Builder baseUrl(String baseUrl) {
            config.setBaseUrl(baseUrl);
            return this;
        }

        /**
         * 添加默认请求头。
         *
         * @param name 请求头名称
         * @param value 请求头值
         * @return 构建器
         */
        public Builder defaultHeader(String name, String value) {
            config.addDefaultHeader(name, value);
            return this;
        }

        /**
         * 设置连接超时（秒）。
         *
         * @param seconds 超时秒数
         * @return 构建器
         */
        public Builder connectTimeout(int seconds) {
            config.setConnectTimeout(java.time.Duration.ofSeconds(seconds));
            return this;
        }

        /**
         * 设置读取超时（秒）。
         *
         * @param seconds 超时秒数
         * @return 构建器
         */
        public Builder readTimeout(int seconds) {
            config.setReadTimeout(java.time.Duration.ofSeconds(seconds));
            return this;
        }

        /**
         * 设置每主机最大请求数。
         *
         * @param maxRequestsPerHost 每主机最大请求数
         * @return 构建器
         */
        public Builder maxRequestsPerHost(int maxRequestsPerHost) {
            config.setMaxRequestsPerHost(maxRequestsPerHost);
            return this;
        }

        /**
         * 设置最大请求数。
         *
         * @param maxRequests 最大请求数
         * @return 构建器
         */
        public Builder maxRequests(int maxRequests) {
            config.setMaxRequests(maxRequests);
            return this;
        }

        /**
         * 设置是否自动重试连接失败。
         *
         * @param retry 是否自动重试
         * @return 构建器
         */
        public Builder retryOnConnectionFailure(boolean retry) {
            config.setRetryOnConnectionFailure(retry);
            return this;
        }

        /**
         * 设置是否信任所有证书。
         * 注意：在生产环境下不推荐信任所有证书，这可能导致安全风险。
         *
         * @param trustAllCerts 是否信任所有证书
         * @return 构建器
         */
        public Builder trustAllCerts(boolean trustAllCerts) {
            config.setTrustAllCerts(trustAllCerts);
            return this;
        }

        /**
         * 构建 API 客户端。
         *
         * @return API 客户端
         */
        public ApiClient build() {
            return buildClient(config);
        }

    }

}