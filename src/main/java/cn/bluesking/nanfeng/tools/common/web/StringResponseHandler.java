package cn.bluesking.nanfeng.tools.common.web;

import java.util.Optional;

import jakarta.annotation.Nonnull;

import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import cn.bluesking.nanfeng.tools.common.utils.JsonUtils;

/**
 * 字符串返回值处理器，专门处理返回类型为 String 的情况。
 * <p>需要单独处理是因为 Spring 使用 StringHttpMessageConverter 处理字符串，。
 * <p>它无法将 Response 对象转换回字符串，需要手动转 JSON。
 *
 * <AUTHOR>
 */
//@RestControllerAdvice
public class StringResponseHand<PERSON> implements ResponseBodyAdvice<Object> {

    /*-------------------- public method --------------------*/

    @Override
    public boolean supports(MethodParameter returnType,
                            @Nonnull Class<? extends HttpMessageConverter<?>> converterType) {
        // 只处理返回类型为 String 的方法，并且不处理已经是 Response 类型的返回值。
        return returnType.getParameterType().equals(String.class);
    }

    @Override
    public Object beforeBodyWrite(Object body,
                                  @Nonnull MethodParameter returnType,
                                  @Nonnull MediaType selectedContentType,
                                  @Nonnull Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  @Nonnull ServerHttpRequest request,
                                  @Nonnull ServerHttpResponse response) {

        // 手动设置响应的 Content-Type 为 application/json
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);

        // 如果已经是 Response 类型，则直接转换为 JSON 字符串返回
        if (body instanceof Response) {
            return JsonUtils.toJson(body);
        }

        else if (body instanceof Optional<?>) {
            return JsonUtils.toJson(Response.success(((Optional<?>) body).orElse(null)));
        }

        else {
            // 其他情况，将字符串包装成 Response 后转为 JSON 字符串返回
            return JsonUtils.toJson(Response.success(body));
        }

    }

}