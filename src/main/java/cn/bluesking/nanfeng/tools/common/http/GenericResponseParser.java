package cn.bluesking.nanfeng.tools.common.http;

import java.io.IOException;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.bluesking.nanfeng.tools.common.utils.JsonUtils;

/**
 * 通用响应解析器实现，提供 JSON 解析和常见错误处理功能。
 * <p>支持多种响应格式和自定义解析逻辑。
 *
 * @param <T> 响应解析后的类型
 * <AUTHOR>
 */
public class GenericResponseParser<T> implements ResponseParser<T> {

    private static final Logger logger = LoggerFactory.getLogger(GenericResponseParser.class);

    /**
     * 目标响应类型。
     */
    private final Class<T> targetType;

    /**
     * 自定义解析函数。
     */
    private final Function<String, T> parseFunction;

    /**
     * 自定义错误解析函数。
     */
    private final Function<String, Exception> errorParseFunction;

    /**
     * 请求 ID。
     */
    private final String requestId;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数。
     *
     * @param targetType 目标响应类型
     * @param parseFunction 自定义解析函数
     * @param errorParseFunction 自定义错误解析函数
     * @param requestId 请求 ID
     */
    private GenericResponseParser(Class<T> targetType,
                                  Function<String, T> parseFunction,
                                  Function<String, Exception> errorParseFunction,
                                  String requestId) {

        this.targetType = targetType;
        this.parseFunction = parseFunction;
        this.errorParseFunction = errorParseFunction;
        this.requestId = requestId;
    }

    /*-------------------- public static method --------------------*/

    /**
     * 创建 JSON 响应解析器。
     *
     * @param targetType 目标响应类型
     * @param <R> 响应类型
     * @return JSON 响应解析器
     */
    public static <R> GenericResponseParser<R> json(Class<R> targetType) {
        return new GenericResponseParser<>(targetType, null, null, null);
    }

    /**
     * 创建 JSON 响应解析器（带请求 ID）。
     *
     * @param targetType 目标响应类型
     * @param requestId 请求 ID
     * @param <R> 响应类型
     * @return JSON 响应解析器
     */
    public static <R> GenericResponseParser<R> json(Class<R> targetType, String requestId) {
        return new GenericResponseParser<>(targetType, null, null, requestId);
    }

    /**
     * 创建自定义响应解析器。
     *
     * @param parseFunction 自定义解析函数
     * @param <R> 响应类型
     * @return 自定义响应解析器
     */
    public static <R> GenericResponseParser<R> custom(Function<String, R> parseFunction) {
        return new GenericResponseParser<>(null, parseFunction, null, null);
    }

    /**
     * 创建自定义响应解析器（带请求 ID）。
     *
     * @param parseFunction 自定义解析函数
     * @param requestId 请求 ID
     * @param <R> 响应类型
     * @return 自定义响应解析器
     */
    public static <R> GenericResponseParser<R> custom(Function<String, R> parseFunction, String requestId) {
        return new GenericResponseParser<>(null, parseFunction, null, requestId);
    }

    /*-------------------- public method --------------------*/

    /**
     * 设置自定义错误解析函数。
     *
     * @param errorParseFunction 自定义错误解析函数
     * @return 配置后的解析器
     */
    public GenericResponseParser<T> withErrorParser(Function<String, Exception> errorParseFunction) {
        return new GenericResponseParser<>(targetType, parseFunction, errorParseFunction, requestId);
    }

    /**
     * 设置 requestId。
     *
     * @param requestId 请求 ID
     * @return 配置后的解析器
     */
    public GenericResponseParser<T> withRequestId(String requestId) {
        return new GenericResponseParser<>(targetType, parseFunction, errorParseFunction, requestId);
    }

    @Override
    public T parse(String responseBody) {

        try {

            // 优先使用自定义解析函数
            if (parseFunction != null) {
                return parseFunction.apply(responseBody);
            }

            // 默认使用 JSON 解析
            if (targetType != null) {
                return JsonUtils.fromJson(responseBody, targetType);
            }

            throw new IllegalStateException("未配置解析方法且未指定目标类型。");
        }
        catch (Exception e) {
            logger.error("failed to parse response, requestId: {}, body: {}", requestId, responseBody, e);
            throw new RuntimeException("解析响应失败：" + e.getMessage(), e);
        }

    }

    @Override
    public Exception parseError(String responseBody) {

        if (errorParseFunction != null) {
            return errorParseFunction.apply(responseBody);
        }

        // 尝试解析常见错误格式
        try {

            // 尝试解析为标准错误响应（包含 code 和 message 字段）
            ErrorResponse errorResponse = JsonUtils.fromJson(responseBody, ErrorResponse.class);
            if (errorResponse != null && errorResponse.getMessage() != null) {
                return new RuntimeException("API 错误：" + errorResponse.getMessage() +
                    "（code: " + errorResponse.getCode() + "）");
            }

        }
        catch (Exception e) {
            // 忽略解析错误
        }

        // 兜底逻辑
        return new RuntimeException("API 错误：" + responseBody);
    }

    @Override
    public String getRequestId() {
        return requestId;
    }

    /*-------------------- inner class --------------------*/

    /**
     * 标准错误响应结构。
     */
    private static class ErrorResponse {

        private String code;

        private String message;

        /*-------------------- getter --------------------*/

        public String getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }

        /*-------------------- setter --------------------*/

        public void setCode(String code) {
            this.code = code;
        }

        public void setMessage(String message) {
            this.message = message;
        }

    }

}