package cn.bluesking.nanfeng.tools.common.utils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Type;

import com.google.gson.Gson;
import com.google.gson.JsonElement;

/**
 * Json 相关工具类。
 *
 * <AUTHOR>
 */
public final class JsonUtils {

    /*-------------------- constructor --------------------*/

    private JsonUtils() {
        throw new IllegalStateException("Utility class");
    }

    /*-------------------- public static method --------------------*/

    public static String toJson(Object object) {
        return getGson().toJson(object);
    }

    public static <T> T fromJson(JsonElement json, Class<T> clazz) {
        return getGson().fromJson(json, clazz);
    }

    public static <T> T fromJson(String json, Class<T> clazz) {
        return getGson().fromJson(json, clazz);
    }

    public static <T> T fromJson(String json, Type type) {
        return getGson().fromJson(json, type);
    }

    /*-------------------- private static method --------------------*/

    private static Gson getGson() {
        return GsonUtils.getGson();
    }

    private static Gson getSimpleGson() {
        return GsonUtils.getSimpleGson();
    }

    /*-------------------- inner class --------------------*/

    /**
     * <AUTHOR>
     */
    @Target({ ElementType.ANNOTATION_TYPE, ElementType.METHOD, ElementType.CONSTRUCTOR, ElementType.FIELD })
    @Retention(RetentionPolicy.RUNTIME)
    @com.fasterxml.jackson.annotation.JsonIgnore
    public @interface JsonIgnore {
    }

}