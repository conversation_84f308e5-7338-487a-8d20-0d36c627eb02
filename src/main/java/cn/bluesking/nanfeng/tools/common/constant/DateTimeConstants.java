package cn.bluesking.nanfeng.tools.common.constant;

/**
 * 日期时间格式常量类。
 *
 * <AUTHOR>
 */
public final class DateTimeConstants {

    /**
     * 默认日期时间格式：yyyy-MM-dd HH:mm:ss.SSSSSS。
     */
    public static final String DEFAULT_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss.SSSSSS";

    /**
     * 默认日期格式：yyyy-MM-dd。
     */
    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 默认时间格式：HH:mm:ss.SSSSSS。
     */
    public static final String DEFAULT_TIME_FORMAT = "HH:mm:ss.SSSSSS";

    /**
     * 简化日期时间格式：yyyy-MM-dd HH:mm:ss。
     */
    public static final String SIMPLE_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 简化时间格式：HH:mm:ss。
     */
    public static final String SIMPLE_TIME_FORMAT = "HH:mm:ss";

    /**
     * 紧凑日期时间格式：yyyyMMddHHmmss。
     */
    public static final String COMPACT_DATE_TIME_FORMAT = "yyyyMMddHHmmss";

    /**
     * 紧凑日期格式：yyyyMMdd。
     */
    public static final String COMPACT_DATE_FORMAT = "yyyyMMdd";

    /**
     * 紧凑时间格式：HHmmss。
     */
    public static final String COMPACT_TIME_FORMAT = "HHmmss";

    /**
     * 中文日期时间格式：yyyy年MM月dd日 HH时mm分ss秒。
     */
    public static final String CHINESE_DATE_TIME_FORMAT = "yyyy年MM月dd日 HH时mm分ss秒";

    /**
     * 中文日期格式：yyyy年MM月dd日。
     */
    public static final String CHINESE_DATE_FORMAT = "yyyy年MM月dd日";

    /**
     * 中文时间格式：HH时mm分ss秒。
     */
    public static final String CHINESE_TIME_FORMAT = "HH时mm分ss秒";

    /**
     * 私有构造函数，防止实例化。
     */
    private DateTimeConstants() {
        throw new UnsupportedOperationException("常量类不应被实例化");
    }

}