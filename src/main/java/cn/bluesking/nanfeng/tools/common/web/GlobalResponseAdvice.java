package cn.bluesking.nanfeng.tools.common.web;

import jakarta.annotation.Nonnull;

import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 全局响应处理器，自动将控制器返回的数据包装为统一的响应格式。
 * <p>不会对 ResponseEntity 和 Response 类型进行包装。
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalResponseAdvice implements ResponseBodyAdvice<Object> {

    /*-------------------- public method --------------------*/

    @Override
    public boolean supports(MethodParameter returnType,
                            @Nonnull Class<? extends HttpMessageConverter<?>> converterType) {
        // 如果返回值已经是 Response 则不需要再次包装
        Class<?> parameterType = returnType.getParameterType();
        return !parameterType.equals(Response.class);
    }

    @Override
    public Object beforeBodyWrite(Object body,
                                  @Nonnull MethodParameter returnType,
                                  @Nonnull MediaType selectedContentType,
                                  @Nonnull Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  @Nonnull ServerHttpRequest request,
                                  @Nonnull ServerHttpResponse response) {

        if (selectedContentType.isCompatibleWith(MediaType.APPLICATION_JSON)) {
            if (body instanceof ResponseEntity<?> respEntity) {

                // 1）解包获取真实数据
                Object innerBody = respEntity.getBody();

                // 2）对 innerBody 进行统一包装
                Response<?> wrapped = wrap(innerBody);

                // 3）重新构建 ResponseEntity，保留状态码和头信息
                return ResponseEntity.status(respEntity.getStatusCode())
                           .headers(respEntity.getHeaders())
                           .body(wrapped);
            }

            else {
                return wrap(body);
            }

        }

        else {
            return body;
        }

    }

    /*-------------------- private method --------------------*/

    private Response<?> wrap(Object body) {

        // 如果返回值为 null，则返回一个空的成功响应。
        if (body == null) {
            return Response.success();
        }

        // 如果返回值已经是 Response 类型，则直接返回。
        else if (body instanceof Response) {
            return (Response<?>) body;
        }

        // 对于 String 类型的返回值，需要特殊处理，因为 String 类型无法直接转换为 Response。
        else if (body instanceof String) {
            return Response.success((String) body);
        }

        else {
            // 其他类型的返回值，都包装为 Response。
            return Response.success(body);
        }

    }

}