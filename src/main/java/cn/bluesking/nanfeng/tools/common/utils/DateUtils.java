package cn.bluesking.nanfeng.tools.common.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 时间日期相关工具类。
 * <p>提供日期时间格式化和解析的工具方法。
 *
 * <AUTHOR>
 * @date 2024-02-29
 * @since 1.0.0
 */
public final class DateUtils {

    /**
     * 紧凑日期时间格式：yyyyMMddHHmmss。
     */
    private static final DateTimeFormatter yyyyMMddHHmmss = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * 私有构造函数，防止实例化。
     */
    private DateUtils() {
        throw new IllegalStateException("Utility class");
    }

    /*-------------------- public static method --------------------*/

    /**
     * 将日期时间格式化为紧凑格式（yyyyMMddHHmmss）。
     *
     * @param dateTime 日期时间
     * @return 格式化后的日期时间字符串
     */
    public static String formatCompactDateTime(LocalDateTime dateTime) {
        return yyyyMMddHHmmss.format(dateTime);
    }

}