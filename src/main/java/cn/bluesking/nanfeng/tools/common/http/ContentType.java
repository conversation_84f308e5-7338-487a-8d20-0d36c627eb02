package cn.bluesking.nanfeng.tools.common.http;

import java.nio.charset.Charset;

/**
 * HTTP 内容类型枚举，定义常用的 HTTP Content-Type 值。
 *
 * <AUTHOR>
 */
public enum ContentType {

    /**
     * 表示 JSON 格式的内容类型 (application/json)。
     */
    JSON("application/json"),

    /**
     * 表示 URL 编码表单数据格式的内容类型 (application/x-www-form-urlencoded)。
     */
    FORM_URLENCODED("application/x-www-form-urlencoded"),

    /**
     * 表示文本内容类型 (text/plain)。
     */
    TEXT_PLAIN("text/plain"),

    /**
     * 表示 HTML 内容类型 (text/html)。
     */
    TEXT_HTML("text/html"),

    /**
     * 表示 XML 内容类型 (application/xml)。
     */
    XML("application/xml"),

    /**
     * 表示多部分表单数据，用于文件上传 (multipart/form-data)。
     */
    MULTIPART_FORM_DATA("multipart/form-data");

    /**
     * Content-Type 值。
     */
    private final String value;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数。
     *
     * @param value Content-Type 值
     */
    ContentType(String value) {
        this.value = value;
    }

    /*-------------------- getter --------------------*/

    /**
     * 获取 Content-Type 值。
     *
     * @return Content-Type 值
     */
    public String getValue() {
        return value;
    }

    /*-------------------- public method --------------------*/

    /**
     * 获取带字符集的 Content-Type 值，格式为 『type;charset=charset』。
     *
     * @param charset 字符集
     * @return 带字符集的 Content-Type 值
     */
    public String getValueWithCharset(Charset charset) {
        return charset == null ? getValue() : getValue() + ";charset=" + charset;
    }

}