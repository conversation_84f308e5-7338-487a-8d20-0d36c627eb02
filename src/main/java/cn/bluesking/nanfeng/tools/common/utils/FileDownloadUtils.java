package cn.bluesking.nanfeng.tools.common.utils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import org.springframework.http.HttpHeaders;

/**
 * 文件下载工具类，处理文件名编码等问题。
 *
 * <AUTHOR>
 */
public final class FileDownloadUtils {

    /*-------------------- constructor --------------------*/

    private FileDownloadUtils() {
        throw new IllegalStateException("Utility class");
    }

    /*-------------------- public static method --------------------*/

    /**
     * 设置下载文件的 Content-Disposition 头，处理各种浏览器兼容性问题。
     *
     * @param headers HTTP 头信息
     * @param filename 文件名
     */
    public static void setContentDispositionHeader(HttpHeaders headers, String filename) {

        // 仅使用 filename* 方式，这是最安全的做法 (RFC 6266)
        // 不再在头部中包含原始中文文件名，只使用编码后的版本
        String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8)
                                     .replaceAll("\\+", "%20");

        // Content-Disposition: attachment; filename*=UTF-8''encoded-filename
        headers.add(HttpHeaders.CONTENT_DISPOSITION,
            "attachment; filename=\"" + encodedFilename + "\"; " + "filename*=UTF-8''" + encodedFilename);

    }

    /*-------------------- private static method --------------------*/

}