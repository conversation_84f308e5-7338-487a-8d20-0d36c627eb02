package cn.bluesking.nanfeng.tools.common.db.converter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.persistence.Converter;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import cn.bluesking.nanfeng.tools.common.db.IdentityEnum;

/**
 * 基于 {@link IdentityEnum} 的枚举列表属性转换器。
 * <p>用于将枚举列表转换为数据库中的字符串表示形式，通常是逗号分隔的 ID 列表。
 *
 * @param <T> 枚举类型，必须实现 {@link IdentityEnum}。
 * <AUTHOR>
 * @date 2025-06-16
 * @since 2.1.10
 */
@Converter
public abstract class IdentityEnumListAttributeConverter<T extends IdentityEnum>
    extends BaseEnumAttributeConverter<List<T>, String> {

    /**
     * 默认分隔符，用于将枚举 ID 转换为字符串时使用。
     */
    protected static final String DEFAULT_SEPARATOR = ",";

    /**
     * 分隔符，用于将枚举 ID 转换为字符串时使用。
     */
    private final String separator;

    /**
     * ID 到枚举的映射，用于快速查找枚举实例。
     */
    private final Map<Integer, T> idToEnumMap;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数，使用默认分隔符和验证模式。
     *
     * @param values 枚举值数组
     */
    protected IdentityEnumListAttributeConverter(T[] values) {
        this(DEFAULT_SEPARATOR, values);
    }

    /**
     * 构造函数，使用指定分隔符和验证模式。
     *
     * @param separator 分隔符
     * @param values 枚举值数组
     */
    protected IdentityEnumListAttributeConverter(String separator, T[] values) {
        this(separator, ValidationMode.LENIENT, values);
    }

    /**
     * 构造函数，使用指定分隔符、验证模式和枚举值数组。
     *
     * @param separator 分隔符
     * @param validationMode 验证模式
     * @param values 枚举值数组
     */
    protected IdentityEnumListAttributeConverter(String separator, ValidationMode validationMode, T[] values) {

        super(values[0].getClass(), validationMode);
        this.separator = separator;
        this.idToEnumMap = Arrays.stream(values)
                               .collect(Collectors.toMap(IdentityEnum::getId, Function.identity()));
    }

    /*-------------------- protected method --------------------*/

    @Override
    protected String innerConvertToDatabaseColumn(List<T> exportItems) {

        if (CollectionUtils.isEmpty(exportItems)) {
            return null;

        }

        else {
            return exportItems
                       .stream()
                       .filter(Objects::nonNull)
                       .map(IdentityEnum::getId)
                       .map(String::valueOf)
                       .collect(Collectors.joining(separator));

        }
    }

    @Override
    protected List<T> innerConvertToEntityAttribute(String exportItemsStr) {

        if (StringUtils.isEmpty(exportItemsStr)) {
            return Collections.emptyList();

        }

        else {
            return Arrays.stream(exportItemsStr.split(separator))
                       .map(String::trim)
                       .filter(StringUtils::isNotEmpty)
                       .map(Integer::valueOf)
                       .map(idToEnumMap::get)
                       .filter(Objects::nonNull)
                       .collect(Collectors.toList());

        }
    }

}