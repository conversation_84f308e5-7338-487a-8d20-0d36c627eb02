package cn.bluesking.nanfeng.tools.common.web;

import cn.bluesking.nanfeng.tools.common.utils.RequestIdGenerator;

/**
 * 通用 API 响应对象。
 *
 * <AUTHOR>
 * @param <T> 响应数据类型
 */
public class Response<T> {

    /**
     * 响应状态码。
     */
    private int code;

    /**
     * 响应消息。
     */
    private String message;

    /**
     * 响应数据。
     */
    private T data;

    /**
     * 是否成功。
     */
    private boolean success;

    /**
     * 请求标识，用于追踪和排查问题。
     */
    private String requestId;

    /**
     * 默认构造函数。
     */
    public Response() {
        this.requestId = RequestIdGenerator.generate();
    }

    /**
     * 构造函数。
     *
     * @param code 响应状态码
     * @param message 响应消息
     * @param data 响应数据
     * @param success 是否成功
     */
    public Response(int code, String message, T data, boolean success) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.success = success;
        this.requestId = RequestIdGenerator.generate();
    }

    /**
     * 构造函数。
     *
     * @param code 响应状态码
     * @param message 响应消息
     * @param data 响应数据
     * @param success 是否成功
     * @param requestId 请求标识
     */
    public Response(int code, String message, T data, boolean success, String requestId) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.success = success;
        this.requestId = requestId != null ? requestId : RequestIdGenerator.generate();
    }

    /*-------------------- public static factory method --------------------*/

    /**
     * 创建成功响应。
     *
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> Response<T> success() {
        return new Response<>(200, "操作成功", null, true);
    }

    /**
     * 创建带数据的成功响应。
     *
     * @param <T> 数据类型
     * @param data 响应数据
     * @return 成功响应
     */
    public static <T> Response<T> success(T data) {
        return new Response<>(200, "操作成功", data, true);
    }

    /**
     * 创建带消息的成功响应。
     *
     * @param <T> 数据类型
     * @param message 成功消息
     * @return 成功响应
     */
    public static <T> Response<T> success(String message) {
        return new Response<>(200, message, null, true);
    }

    /**
     * 创建带消息和数据的成功响应。
     *
     * @param <T> 数据类型
     * @param message 成功消息
     * @param data 响应数据
     * @return 成功响应
     */
    public static <T> Response<T> success(String message, T data) {
        return new Response<>(200, message, data, true);
    }

    /**
     * 创建带特定 requestId 的成功响应。
     *
     * @param <T> 数据类型
     * @param data 响应数据
     * @param requestId 请求标识
     * @return 成功响应
     */
    public static <T> Response<T> successWithRequestId(T data, String requestId) {
        return new Response<>(200, "操作成功", data, true, requestId);
    }

    /**
     * 创建失败响应。
     *
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> Response<T> error() {
        return new Response<>(500, "操作失败", null, false);
    }

    /**
     * 创建带消息的失败响应。
     *
     * @param <T> 数据类型
     * @param message 错误消息
     * @return 失败响应
     */
    public static <T> Response<T> error(String message) {
        return new Response<>(500, message, null, false);
    }

    /**
     * 创建带状态码和消息的失败响应。
     *
     * @param <T> 数据类型
     * @param code 错误状态码
     * @param message 错误消息
     * @return 失败响应
     */
    public static <T> Response<T> error(int code, String message) {
        return new Response<>(code, message, null, false);
    }

    /**
     * 创建带状态码、消息和 requestId 的失败响应。
     *
     * @param <T> 数据类型
     * @param code 错误状态码
     * @param message 错误消息
     * @param requestId 请求标识
     * @return 失败响应
     */
    public static <T> Response<T> errorWithRequestId(int code, String message, String requestId) {
        return new Response<>(code, message, null, false, requestId);
    }

    /*-------------------- getter --------------------*/

    /**
     * 获取响应状态码。
     *
     * @return 响应状态码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取响应消息。
     *
     * @return 响应消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 获取响应数据。
     *
     * @return 响应数据
     */
    public T getData() {
        return data;
    }

    /**
     * 获取是否成功。
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return success;
    }

    /**
     * 获取请求标识。
     *
     * @return 请求标识
     */
    public String getRequestId() {
        return requestId;
    }

    /*-------------------- setter --------------------*/

    /**
     * 设置响应状态码。
     *
     * @param code 响应状态码
     */
    public void setCode(int code) {
        this.code = code;
    }

    /**
     * 设置响应消息。
     *
     * @param message 响应消息
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 设置响应数据。
     *
     * @param data 响应数据
     */
    public void setData(T data) {
        this.data = data;
    }

    /**
     * 设置是否成功。
     *
     * @param success 是否成功
     */
    public void setSuccess(boolean success) {
        this.success = success;
    }

    /**
     * 设置请求标识。
     *
     * @param requestId 请求标识
     */
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

}