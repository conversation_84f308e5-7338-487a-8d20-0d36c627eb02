package cn.bluesking.nanfeng.tools.common.utils;

/**
 * 请求标识生成器工具类。
 * <p>提供生成唯一请求标识的工具方法，用于跟踪和记录系统请求。
 *
 * <AUTHOR>
 * @date 2024-02-29
 * @since 1.0.0
 */
public final class RequestIdGenerator {

    /*-------------------- constructor --------------------*/

    /**
     * 私有构造函数，防止实例化。
     */
    private RequestIdGenerator() {
        throw new IllegalStateException("Utility class");
    }

    /*-------------------- public static method --------------------*/

    /**
     * 生成请求标识。
     * <p>格式：时间戳(毫秒)-8位随机字符。
     * <p>这种格式便于排序和查找特定时间段的请求，同时保持足够的随机性以避免冲突。
     *
     * @return 请求标识
     */
    public static String generate() {
        // 获取当前时间的毫秒数
        long timestamp = System.currentTimeMillis();

        // 生成8位随机字符
        String randomPart = Utilities.buildRandomNanoId(8);

        // 组合成 timestamp-randomPart 格式
        return timestamp + "-" + randomPart;
    }

}