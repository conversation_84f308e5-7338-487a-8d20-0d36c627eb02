package cn.bluesking.nanfeng.tools.common.web;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import cn.bluesking.nanfeng.tools.common.exception.BusinessException;
import cn.bluesking.nanfeng.tools.common.utils.RequestIdGenerator;

/**
 * 全局异常处理器，处理应用中的各种异常。
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /*-------------------- public method --------------------*/

    /**
     * 处理业务异常。
     * <p>业务异常是可以直接返回给用户的异常信息。
     *
     * @param e 业务异常
     * @return 统一响应对象
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Response<Void> handleBusinessException(BusinessException e) {
        // 使用异常中的 requestId
        String requestId = e.getRequestId();

        logger.warn("business exception, requestId: {}, code: {}, message: {}",
            requestId, e.getCode(), e.getMessage(), e);

        return Response.errorWithRequestId(e.getCode(), e.getMessage(), requestId);
    }

    /**
     * 处理未知异常。
     * <p>未知异常不会直接返回异常信息给用户，而是返回通用错误提示。
     *
     * @param e 未知异常
     * @return 统一响应对象
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Response<Void> handleException(Exception e) {
        // 为未知异常生成 requestId
        String requestId = RequestIdGenerator.generate();
        logger.error("system error, requestId: {}, message: {}", requestId, e.getMessage(), e);

        return Response.errorWithRequestId(500, "未知异常，请联系客服反馈，requestId：" + requestId, requestId);
    }

}