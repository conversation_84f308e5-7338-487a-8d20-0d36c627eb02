package cn.bluesking.nanfeng.tools.common.exception;

import cn.bluesking.nanfeng.tools.common.utils.RequestIdGenerator;

/**
 * 业务异常基类，所有业务异常都应继承此类。
 * <p>此类型的异常信息是用户友好的，可以直接返回给前端用户查看。
 * <p>包含错误码和请求标识，便于异常追踪和定位问题。
 *
 * <AUTHOR>
 * @date 2024-02-29
 * @since 1.0.0
 */
public class BusinessException extends RuntimeException {

    /**
     * 错误码。
     */
    private final int code;

    /**
     * 请求标识。
     */
    private final String requestId;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数。
     *
     * @param message 错误消息
     */
    public BusinessException(String message) {
        this(500, message);
    }

    /**
     * 构造函数。
     *
     * @param code 错误码
     * @param message 错误消息
     */
    public BusinessException(int code, String message) {
        this(code, message, RequestIdGenerator.generate());
    }

    /**
     * 构造函数。
     *
     * @param code 错误码
     * @param message 错误消息
     * @param cause 原始异常
     */
    public BusinessException(int code, String message, Throwable cause) {
        this(code, message, cause, RequestIdGenerator.generate());
    }

    /**
     * 构造函数。
     *
     * @param code 错误码
     * @param message 错误消息
     * @param requestId 请求标识
     */
    public BusinessException(int code, String message, String requestId) {
        super(message);
        this.code = code;
        this.requestId = requestId;
    }

    /**
     * 构造函数。
     *
     * @param code 错误码
     * @param message 错误消息
     * @param cause 原始异常
     * @param requestId 请求标识
     */
    public BusinessException(int code, String message, Throwable cause, String requestId) {
        super(message, cause);
        this.code = code;
        this.requestId = requestId;
    }

    /*-------------------- getter --------------------*/

    /**
     * 获取错误码。
     *
     * @return 错误码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取请求标识。
     *
     * @return 请求标识
     */
    public String getRequestId() {
        return requestId;
    }

}