package cn.bluesking.nanfeng.tools.common.utils;

import static cn.bluesking.nanfeng.tools.common.utils.ValidationUtils.assertNotNull;
import static cn.bluesking.nanfeng.tools.common.utils.ValidationUtils.assertTrue;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * POI 操作工具类。
 *
 * <AUTHOR>
 */
public final class PoiUtils {

    /*-------------------- constructor --------------------*/

    private PoiUtils() {
        throw new IllegalStateException("Utility class");
    }

    /*-------------------- public static method --------------------*/

    public static Workbook readWorkbook(File file) throws IOException {

        assertNotNull(file);
        assertTrue(file.exists());
        assertTrue(file.isFile());

        String fileName = file.getName();
        String fileType = fileName.substring(fileName.lastIndexOf('.') + 1);

        if (StringUtils.equalsIgnoreCase(fileType, "xls")) {
            return new HSSFWorkbook(new FileInputStream(file));
        }

        else if (StringUtils.equalsIgnoreCase(fileType, "xlsx")) {
            return new XSSFWorkbook(new FileInputStream(file));
        }

        else {
            throw new IllegalArgumentException(fileType);
        }

    }

    public static CellRangeAddress getMergeRegion(Cell cell) {

        int columnIndex = cell.getColumnIndex();
        Row row = cell.getRow();
        Sheet sheet = row.getSheet();
        for (CellRangeAddress mergedRegion : sheet.getMergedRegions()) {

            int firstRow = mergedRegion.getFirstRow();
            int lastRow = mergedRegion.getLastRow();

            int firstColumn = mergedRegion.getFirstColumn();
            int lastColumn = mergedRegion.getLastColumn();

            if (between(row.getRowNum(), firstRow, lastRow) && between(columnIndex, firstColumn, lastColumn)) {
                return mergedRegion;
            }

        }

        return null;
    }

    public static Object getCellValue(Cell cell) {

        if (cell == null) {
            return null;
        }

        else {

            CellType cellType = cell.getCellType();
            return switch(cellType) {
                case STRING -> cell.getStringCellValue();
                case NUMERIC -> cell.getNumericCellValue();
                case BOOLEAN -> cell.getBooleanCellValue();
                case BLANK, FORMULA -> StringUtils.EMPTY;
                default -> throw new UnsupportedOperationException("unsupported CellType: " + cellType);
            };

        }

    }

    public static String getCellStringValue(Cell cell) {
        return StringUtils.trim(String.valueOf(getCellValue(cell)));
    }

    /*-------------------- private static method --------------------*/

    private static boolean between(int judgingValue, int startInclude, int endInclude) {
        return startInclude <= judgingValue && judgingValue <= endInclude;
    }

}