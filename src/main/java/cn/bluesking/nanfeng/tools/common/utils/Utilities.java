package cn.bluesking.nanfeng.tools.common.utils;

import static com.aventrix.jnanoid.jnanoid.NanoIdUtils.DEFAULT_NUMBER_GENERATOR;

import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections4.MapUtils;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.google.gson.reflect.TypeToken;

/**
 * 常用工具类。
 *
 * <AUTHOR>
 */
public final class Utilities {

    /** 浮点数比较精度 */
    private static final double EPSILON = 0.0001;

    public static final char[] TRADE_ORDER_LIST_SUPPORT_CHAR = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".toCharArray();

    public static final Type MAP_TYPE = new TypeToken<Map<String, Object>>() {}.getType();

    /*-------------------- constructor --------------------*/

    private Utilities() {
        throw new IllegalStateException("Utility class");
    }

    /*-------------------- public static method --------------------*/

    /**
     * 生成指定长度的随机字符串。
     */
    public static String buildRandomNanoId(int length) {
        return NanoIdUtils.randomNanoId(DEFAULT_NUMBER_GENERATOR, TRADE_ORDER_LIST_SUPPORT_CHAR, length);
    }

    @SafeVarargs
    public static <E> List<E> ofList(E... elements) {
        return elements == null || elements.length == 0
                   ? Collections.emptyList()
                   : Collections.unmodifiableList(Arrays.asList(elements));
    }

    @SafeVarargs
    public static <E> Set<E> ofSet(E... elements) {
        return elements == null || elements.length == 0
                   ? Collections.emptySet()
                   : Collections.unmodifiableSet(new HashSet<>(Arrays.asList(elements)));
    }

    @SuppressWarnings("unchecked")
    public static <K, V> Map<K, V> ofMap(Object... elements) {

        if (elements == null || elements.length == 0) {
            return Collections.emptyMap();
        }

        else if ((elements.length & 1) != 0) {
            throw new IllegalArgumentException("key and value must appear in pairs!");
        }

        else {

            Map<K, V> resultMap = new HashMap<>();
            for (int i = 0; i < elements.length; i += 2) {

                K key = (K) elements[i];
                V value = (V) elements[i + 1];

                resultMap.put(key, value);
            }

            return Collections.unmodifiableMap(resultMap);
        }

    }

    public static Map<String, Object> toMap(Object source) {
        return source == null
                   ? new HashMap<>()
                   : JsonUtils.fromJson(JsonUtils.toJson(source), MAP_TYPE);
    }

    public static <T> T fromMap(Map<String, Object> map, Type type) {
        return MapUtils.isEmpty(map) ? null : JsonUtils.fromJson(JsonUtils.toJson(map), type);
    }

    public static boolean isLargeThanZero(Double value) {
        return value != null && value > EPSILON;
    }

    public static String capitalFirstChar(String str) {

        if(str == null) {
            return null;
        }

        char[] s = str.toCharArray();
        // 首字母是大写字母
        if(s[0] >= 'A' && s[0] <= 'Z') {
            // s[0] -= 32;这里可以用位运算代替
            s[0] ^= 32;
        }

        return String.valueOf(s);
    }

    /*-------------------- private static method --------------------*/

}