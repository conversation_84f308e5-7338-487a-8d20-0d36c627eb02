package cn.bluesking.nanfeng.tools.common.exception;

import cn.bluesking.nanfeng.tools.common.utils.RequestIdGenerator;

/**
 * 参数校验异常，用于表示参数校验失败。
 * <p>此类型的异常信息是用户友好的，可以直接返回给前端用户查看。
 * <p>继承自 BusinessException，使用 400 作为默认错误码。
 *
 * <AUTHOR>
 * @date 2024-02-29
 * @since 1.0.0
 */
public class ValidationException extends BusinessException {

    /**
     * 默认错误码 - 400 表示参数错误。
     */
    private static final int DEFAULT_ERROR_CODE = 400;

    /**
     * 默认错误消息。
     */
    private static final String DEFAULT_ERROR_MESSAGE = "参数校验失败";

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数，使用默认错误码和消息。
     */
    public ValidationException() {
        super(DEFAULT_ERROR_CODE, DEFAULT_ERROR_MESSAGE);
    }

    /**
     * 构造函数，使用默认错误码和指定消息。
     *
     * @param message 错误消息
     */
    public ValidationException(String message) {
        super(DEFAULT_ERROR_CODE, message);
    }

    /**
     * 构造函数，使用指定错误码和消息。
     *
     * @param code 错误码
     * @param message 错误消息
     */
    public ValidationException(int code, String message) {
        super(code, message);
    }

    /**
     * 构造函数，使用默认错误码、指定消息和原始异常。
     *
     * @param message 错误消息
     * @param cause 原始异常
     */
    public ValidationException(String message, Throwable cause) {
        super(DEFAULT_ERROR_CODE, message, cause);
    }

    /**
     * 构造函数，使用指定错误码、消息和原始异常。
     *
     * @param code 错误码
     * @param message 错误消息
     * @param cause 原始异常
     */
    public ValidationException(int code, String message, Throwable cause) {
        super(code, message, cause);
    }

    /**
     * 构造函数，使用指定错误码、消息和请求标识。
     *
     * @param code 错误码
     * @param message 错误消息
     * @param requestId 请求标识
     */
    public ValidationException(int code, String message, String requestId) {
        super(code, message, requestId);
    }

    /**
     * 构造函数，使用指定错误码、消息、原始异常和请求标识。
     *
     * @param code 错误码
     * @param message 错误消息
     * @param cause 原始异常
     * @param requestId 请求标识
     */
    public ValidationException(int code, String message, Throwable cause, String requestId) {
        super(code, message, cause, requestId);
    }

}