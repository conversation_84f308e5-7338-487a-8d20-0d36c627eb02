package cn.bluesking.nanfeng.tools.common.db.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * 基础枚举属性转换器。
 * <p>提供枚举与数据库值之间的基础转换逻辑。
 *
 * @param <T> 枚举类型
 * @param <V> 数据库存储类型
 * <AUTHOR>
 * @date 2025-06-17
 * @since 2.1.10
 */
@Converter
abstract class BaseEnumAttributeConverter<T, V> implements AttributeConverter<T, V> {

    /**
     * 验证模式。
     */
    private final ValidationMode validationMode;

    /**
     * 枚举类型。
     */
    private final Class<?> enumClass;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数。
     *
     * @param enumClass 枚举类型
     * @param validationMode 验证模式
     */
    protected BaseEnumAttributeConverter(Class<?> enumClass, ValidationMode validationMode) {

        this.enumClass = enumClass;
        this.validationMode = validationMode;
    }

    /*-------------------- public method --------------------*/

    /**
     * 将数据库中的值转换为对应的枚举值。
     *
     * @param dbData 数据库中的值
     * @return 对应的枚举值
     */
    @Override
    public T convertToEntityAttribute(V dbData) {

        if (dbData == null) {
            return null;

        }

        else {

            T attribute = innerConvertToEntityAttribute(dbData);
            if (ValidationMode.STRICT.equals(validationMode) && attribute == null) {
                throw new IllegalArgumentException(
                    "Cannot convert value [" + dbData + "] to enum type " + enumClass.getSimpleName());

            }
            return attribute;

        }
    }

    /**
     * 将枚举值转换为数据库存储值。
     *
     * @param attribute 枚举值
     * @return 数据库存储值
     */
    @Override
    public V convertToDatabaseColumn(T attribute) {
        return innerConvertToDatabaseColumn(attribute);
    }

    /*-------------------- protected method --------------------*/

    /**
     * 内部方法，将数据库值转换为枚举值。
     * <p>子类需要实现具体的转换逻辑。
     *
     * @param dbData 数据库中的值
     * @return 对应的枚举值
     */
    protected abstract T innerConvertToEntityAttribute(V dbData);

    /**
     * 内部方法，将枚举值转换为数据库存储值。
     * <p>子类需要实现具体的转换逻辑。
     *
     * @param attribute 枚举值
     * @return 数据库存储值
     */
    protected abstract V innerConvertToDatabaseColumn(T attribute);

    /*-------------------- inner class --------------------*/

    /**
     * 枚举值验证模式。
     * <p>控制在将数据库值转换为枚举时，如何处理无法匹配的值。
     */
    protected enum ValidationMode {

        /**
         * 严格模式：当数据库值无法匹配到枚举时抛出异常。
         */
        STRICT,

        /**
         * 宽松模式：当数据库值无法匹配到枚举时返回 null。
         */
        LENIENT

    }

}