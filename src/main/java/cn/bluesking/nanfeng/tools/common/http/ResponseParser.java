package cn.bluesking.nanfeng.tools.common.http;

/**
 * HTTP 响应解析器接口，负责将 HTTP 响应体解析为指定类型的对象。
 * 提供了错误处理和请求跟踪功能。
 *
 * @param <T> 响应解析后的类型
 * <AUTHOR>
 */
public interface ResponseParser<T> {

    /**
     * 解析响应体文本。
     *
     * @param responseBody 响应体文本
     * @return 解析后的对象
     */
    T parse(String responseBody);

    /**
     * 尝试将失败响应解析为异常。
     * 当 HTTP 请求返回非成功状态码（非 200-299）时，调用此方法尝试从响应中提取错误信息。
     *
     * @param responseBody 响应体文本
     * @return 解析出的异常或 null（如果返回 null，则 ApiClient 会抛出一个标准 IOException）。
     */
    default Exception parseError(String responseBody) {
        return null;
    }

    /**
     * 获取请求 ID，用于追踪请求。
     *
     * @return 请求 ID
     */
    default String getRequestId() {
        return null;
    }

}