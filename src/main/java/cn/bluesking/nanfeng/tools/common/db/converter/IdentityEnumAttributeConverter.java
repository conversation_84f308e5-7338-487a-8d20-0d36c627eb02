package cn.bluesking.nanfeng.tools.common.db.converter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.persistence.Converter;

import cn.bluesking.nanfeng.tools.common.db.IdentityEnum;

/**
 * 标识枚举属性转换器。
 * <p>用于将标识枚举类型转换为数据库存储格式。
 *
 * <AUTHOR>
 * @date 2025-06-16
 * @since 2.1.10
 */
@Converter
public abstract class IdentityEnumAttributeConverter<T extends IdentityEnum>
    extends BaseEnumAttributeConverter<T, Integer> {

    /**
     * ID 到枚举值的映射。
     * <p>用于快速查找枚举值。
     */
    private final Map<Integer, T> idToEnumMap;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数。
     * <p>使用宽松验证模式（LENIENT）。
     *
     * @param values 枚举值数组
     */
    protected IdentityEnumAttributeConverter(T[] values) {
        this(ValidationMode.LENIENT, values);
    }

    /**
     * 构造函数。
     * <p>可以指定验证模式。
     *
     * @param validationMode 验证模式
     * @param values 枚举值数组
     */
    protected IdentityEnumAttributeConverter(ValidationMode validationMode, T[] values) {

        super(values[0].getClass(), validationMode);
        this.idToEnumMap = Arrays.stream(values)
                               .collect(Collectors.toMap(IdentityEnum::getId, Function.identity()));
    }

    /*-------------------- protected method --------------------*/

    @Override
    protected Integer innerConvertToDatabaseColumn(T attribute) {
        return attribute == null ? null : attribute.getId();
    }

    @Override
    protected T innerConvertToEntityAttribute(Integer dbData) {
        return idToEnumMap.get(dbData);

    }

}