package cn.bluesking.nanfeng.tools.common.utils;

import java.util.Collection;
import java.util.Objects;
import java.util.function.Supplier;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.routines.EmailValidator;

import cn.bluesking.nanfeng.tools.common.exception.ValidationException;

/**
 * 参数校验相关操作的工具类。
 * <p>提供各种类型参数的校验方法，包括：空值检查、相等性比较、类型有效性检查等。
 * <p>校验不通过时会抛出 ValidationException 异常。
 *
 * <AUTHOR>
 * @date 2024-02-29
 * @since 1.0.0
 */
public final class ValidationUtils {

    /**
     * 浮点数比较允许的误差。
     */
    private static final double DIFF = 1e-6;

    /**
     * 邮箱验证器，严格校验模式：不允许 mail@com，仅接受如 <EMAIL>，要求域名必须包含二级及以上标签，且末尾的 TLD 要符合 IANA 列表。
     */
    private static final EmailValidator emailValidator = EmailValidator.getInstance(false, false);

    /*-------------------- constructor --------------------*/

    /**
     * 私有构造函数，防止实例化。
     */
    private ValidationUtils() {
        throw new IllegalStateException("Utility class");
    }

    /*-------------------- public static method --------------------*/

    /**
     * 构建无消息的验证异常。
     *
     * @return 验证异常
     */
    public static ValidationException buildError() {
        return new ValidationException();
    }

    /**
     * 构建带有指定消息的验证异常。
     *
     * @param message 错误消息
     * @return 验证异常
     */
    public static ValidationException buildError(String message) {
        return new ValidationException(message);
    }

    /*-------------------- email validation --------------------*/

    /**
     * 验证邮箱格式是否不正确。
     *
     * @param email 邮箱地址
     * @return 邮箱格式不正确返回 true，否则返回 false。
     */
    private static boolean isInvalidEmail(String email) {
        return email == null
                   || email.isEmpty()
                   || !emailValidator.isValid(email);
    }

    /**
     * 验证邮箱格式是否正确，如果不正确则抛出异常。
     *
     * @param email 邮箱地址
     * @throws ValidationException 当邮箱格式不正确时抛出此异常
     */
    public static void assertValidEmail(String email) {
        if (isInvalidEmail(email)) {
            throw buildError("邮箱格式不正确");
        }

    }

    /**
     * 验证邮箱格式是否正确，如果不正确则抛出带有自定义消息的异常。
     *
     * @param email 邮箱地址
     * @param message 错误消息
     * @throws ValidationException 当邮箱格式不正确时抛出此异常
     */
    public static void assertValidEmail(String email, String message) {
        if (isInvalidEmail(email)) {
            throw buildError(message);
        }

    }

    /**
     * 验证邮箱格式是否正确，如果不正确则抛出带有自定义消息的异常。
     *
     * @param email 邮箱地址
     * @param messageSupplier 错误消息提供者
     * @throws ValidationException 当邮箱格式不正确时抛出此异常
     */
    public static void assertValidEmail(String email, Supplier<String> messageSupplier) {
        if (isInvalidEmail(email)) {
            throw buildError(messageSupplier.get());
        }

    }

    /*-------------------- assertEquals --------------------*/

    /**
     * 验证两个对象是否相等，如果不相等则抛出异常。
     *
     * @param expect 期望值
     * @param actual 实际值
     * @throws ValidationException 当两个对象不相等时抛出此异常
     */
    public static void assertEquals(Object expect, Object actual) {
        if (!Objects.equals(expect, actual)) {
            throw buildError();
        }

    }

    /**
     * 验证两个对象是否相等，如果不相等则抛出带有自定义消息的异常。
     *
     * @param expect 期望值
     * @param actual 实际值
     * @param messageSupplier 错误消息提供者
     * @throws ValidationException 当两个对象不相等时抛出此异常
     */
    public static void assertEquals(Object expect, Object actual, Supplier<String> messageSupplier) {
        if (!Objects.equals(expect, actual)) {
            throw buildError(messageSupplier.get());
        }

    }

    /**
     * 验证两个对象是否相等，如果不相等则抛出带有自定义消息的异常。
     *
     * @param expect 期望值
     * @param actual 实际值
     * @param message 错误消息
     * @throws ValidationException 当两个对象不相等时抛出此异常
     */
    public static void assertEquals(Object expect, Object actual, String message) {
        if (!Objects.equals(expect, actual)) {
            throw buildError(message);
        }

    }

    public static void assertEquals(int expect, int actual) {
        if (expect != actual) {
            throw buildError();
        }

    }

    public static void assertEquals(int expect, int actual, Supplier<String> messageSupplier) {
        if (expect != actual) {
            throw buildError(messageSupplier.get());
        }

    }

    public static void assertEquals(int expect, int actual, String message) {
        if (expect != actual) {
            throw buildError(message);
        }

    }

    public static void assertEquals(long expect, long actual) {
        if (expect != actual) {
            throw buildError();
        }

    }

    public static void assertEquals(long expect, long actual, Supplier<String> messageSupplier) {
        if (expect != actual) {
            throw buildError(messageSupplier.get());
        }

    }

    public static void assertEquals(long expect, long actual, String message) {
        if (expect != actual) {
            throw buildError(message);
        }

    }

    public static void assertEquals(double expect, double actual) {
        if (notEquals(expect, actual)) {
            throw buildError();
        }

    }

    public static void assertEquals(double expect, double actual, Supplier<String> messageSupplier) {
        if (notEquals(expect, actual)) {
            throw buildError(messageSupplier.get());
        }

    }

    public static void assertEquals(double expect, double actual, String message) {
        if (notEquals(expect, actual)) {
            throw buildError(message);
        }

    }

    private static boolean notEquals(double expect, double actual) {
        return Math.abs(expect - actual) > DIFF;
    }

    /*-------------------- assertTrue --------------------*/

    public static void assertTrue(Boolean condition) {
        if (!Boolean.TRUE.equals(condition)) {
            throw buildError();
        }

    }

    public static void assertTrue(Boolean condition, Supplier<String> messageSupplier) {
        if (!Boolean.TRUE.equals(condition)) {
            throw buildError(messageSupplier.get());
        }

    }

    public static void assertTrue(Boolean condition, String message) {
        if (!Boolean.TRUE.equals(condition)) {
            throw buildError(message);
        }

    }

    /*-------------------- assertFalse --------------------*/

    public static void assertFalse(boolean condition) {
        if (condition) {
            throw buildError();
        }

    }

    public static void assertFalse(boolean condition, Supplier<String> messageSupplier) {
        if (condition) {
            throw buildError(messageSupplier.get());
        }

    }

    public static void assertFalse(boolean condition, String message) {
        if (condition) {
            throw buildError(message);
        }

    }

    /*-------------------- assertEmpty --------------------*/

    public static void assertEmpty(Collection<?> target) {
        if (CollectionUtils.isNotEmpty(target)) {
            throw buildError();
        }

    }

    public static void assertEmpty(Collection<?> target, Supplier<String> messageSupplier) {
        if (CollectionUtils.isNotEmpty(target)) {
            throw buildError(messageSupplier.get());
        }

    }

    public static void assertEmpty(Collection<?> target, String message) {
        if (CollectionUtils.isNotEmpty(target)) {
            throw buildError(message);
        }

    }

    public static void assertEmpty(Object[] array) {
        if (ArrayUtils.isNotEmpty(array)) {
            throw buildError();
        }

    }

    public static void assertEmpty(Object[] array, Supplier<String> messageSupplier) {
        if (ArrayUtils.isNotEmpty(array)) {
            throw buildError(messageSupplier.get());
        }

    }

    public static void assertEmpty(Object[] array, String message) {
        if (ArrayUtils.isNotEmpty(array)) {
            throw buildError(message);
        }

    }

    public static void assertEmpty(String target) {
        if (StringUtils.isNotEmpty(target)) {
            throw buildError();
        }

    }

    public static void assertEmpty(String target, Supplier<String> messageSupplier) {
        if (StringUtils.isNotEmpty(target)) {
            throw buildError(messageSupplier.get());
        }

    }

    public static void assertEmpty(String target, String message) {
        if (StringUtils.isNotEmpty(target)) {
            throw buildError(message);
        }

    }

    /*-------------------- assertNotEmpty --------------------*/

    public static void assertNotEmpty(Collection<?> target) {
        if (CollectionUtils.isEmpty(target)) {
            throw buildError();
        }

    }

    public static void assertNotEmpty(Collection<?> target, Supplier<String> messageSupplier) {
        if (CollectionUtils.isEmpty(target)) {
            throw buildError(messageSupplier.get());
        }

    }

    public static void assertNotEmpty(Collection<?> target, String message) {
        if (CollectionUtils.isEmpty(target)) {
            throw buildError(message);
        }

    }

    public static void assertNotEmpty(Object[] array) {
        if (ArrayUtils.isEmpty(array)) {
            throw buildError();
        }

    }

    public static void assertNotEmpty(Object[] array, Supplier<String> messageSupplier) {
        if (ArrayUtils.isEmpty(array)) {
            throw buildError(messageSupplier.get());
        }

    }

    public static void assertNotEmpty(Object[] array, String message) {
        if (ArrayUtils.isEmpty(array)) {
            throw buildError(message);
        }

    }

    public static void assertNotEmpty(String target) {
        if (StringUtils.isEmpty(target)) {
            throw buildError();
        }

    }

    public static void assertNotEmpty(String target, Supplier<String> messageSupplier) {
        if (StringUtils.isEmpty(target)) {
            throw buildError(messageSupplier.get());
        }

    }

    public static void assertNotEmpty(String target, String message) {
        if (StringUtils.isEmpty(target)) {
            throw buildError(message);
        }

    }

    /*-------------------- assertNotNull --------------------*/

    public static void assertNotNull(Object target) {
        if (Objects.isNull(target)) {
            throw buildError();
        }

    }

    public static void assertNotNull(Object target, Supplier<String> messageSupplier) {
        if (Objects.isNull(target)) {
            throw buildError(messageSupplier.get());
        }

    }

    public static void assertNotNull(Object target, String message) {
        if (Objects.isNull(target)) {
            throw buildError(message);
        }

    }

    /*-------------------- assertNull --------------------*/

    public static void assertNull(Object target) {
        if (Objects.nonNull(target)) {
            throw buildError();
        }

    }

    public static void assertNull(Object target, Supplier<String> messageSupplier) {
        if (Objects.nonNull(target)) {
            throw buildError(messageSupplier.get());
        }

    }

    public static void assertNull(Object target, String message) {
        if (Objects.nonNull(target)) {
            throw buildError(message);
        }

    }

    /*-------------------- other --------------------*/

    public static void fail() {
        throw buildError();
    }

    public static void fail(String message) {
        throw buildError(message);
    }

}