package cn.bluesking.nanfeng.tools.common.utils;

import java.util.UUID;

/**
 * UUID 生成器工具类。
 * <p>提供生成无连字符 UUID 字符串的工具方法。
 *
 * <AUTHOR>
 * @date 2024-02-29
 * @since 1.0.0
 */
public final class UuidGenerator {

    /*-------------------- constructor --------------------*/

    /**
     * 私有构造函数，防止实例化。
     */
    private UuidGenerator() {
        throw new IllegalStateException("Utility class");
    }

    /*-------------------- public static method --------------------*/

    /**
     * 生成一个无连字符的 UUID 字符串。
     *
     * @return 32 位长度的 UUID 字符串
     */
    public static String generate() {
        return UUID.randomUUID().toString().replaceAll("\\-", "");
    }

}