package cn.bluesking.nanfeng.tools.common.utils;

import java.io.*;
import java.nio.charset.Charset;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

import org.apache.commons.io.FileUtils;
import org.apache.poi.util.IOUtils;

/**
 * 压缩文件工具类。
 *
 * <AUTHOR>
 */
public final class CompressedFileUtils {

    /*-------------------- constructor --------------------*/

    private CompressedFileUtils() {
        throw new IllegalStateException("Utility class");
    }

    /*-------------------- public static method --------------------*/

    public static void zipFile(List<File> srcFiles, File desZipFile) throws IOException {

        byte[] buf = new byte[1024];
        try (ZipOutputStream out = new ZipOutputStream(new FileOutputStream(desZipFile))) {

            for (File srcFile : srcFiles) {

                try (FileInputStream in = new FileInputStream(srcFile)) {

                    out.putNextEntry(new ZipEntry(srcFile.getName()));

                    int len;
                    while ((len = in.read(buf)) > 0) {
                        out.write(buf, 0, len);
                    }

                    out.closeEntry();
                }

            }

        }

    }

    public static List<File> unzipFiles(File zipFile, String desDirPath) throws IOException {

        File destDir = new File(desDirPath);
        if (!destDir.exists()) {
            FileUtils.forceMkdir(destDir);
        }

        String destDirAbsolutePath = destDir.getAbsolutePath();
        if (!destDirAbsolutePath.endsWith(File.separator)) {
            destDirAbsolutePath += File.separator;
        }

        try (ZipFile zip = new ZipFile(zipFile, Charset.forName("GBK"))) {

            for (Enumeration<? extends ZipEntry> entries = zip.entries(); entries.hasMoreElements(); ) {

                ZipEntry entry = entries.nextElement();
                // 跳过文件夹，避免多层解压。
                if (!entry.isDirectory()) {

                    String outPath = destDirAbsolutePath + getFileName(entry);

                    try (InputStream in = zip.getInputStream(entry);
                         OutputStream out = new FileOutputStream(outPath);) {

                        IOUtils.copy(in, out);
                    }

                }

            }

        }

        return Optional.ofNullable(destDir.listFiles())
                .map(Arrays::asList)
                .orElseGet(Collections::emptyList)
                .stream()
                .sorted((f1, f2) -> Collator.getInstance(Locale.CHINA).compare(f1.getName(), f2.getName()))
                .collect(Collectors.toList());
    }

    /*-------------------- private static method --------------------*/

    private static String getFileName(ZipEntry entry) {

        String entryName = entry.getName();
        int index = entryName.indexOf('/');
        return index == -1 ? entryName : entryName.substring(index + 1);
    }

}