package cn.bluesking.nanfeng.tools.common.http;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * API 客户端配置类，用于定制 HTTP 客户端的参数和默认行为。
 *
 * <AUTHOR>
 */
public class ApiClientConfig {

    /**
     * 默认连接超时时间（秒）。
     */
    private static final int DEFAULT_CONNECT_TIMEOUT_SECONDS = 5;

    /**
     * 默认读取超时时间（秒）。
     */
    private static final int DEFAULT_READ_TIMEOUT_SECONDS = 30;

    /**
     * 默认每主机最大请求数。
     */
    private static final int DEFAULT_MAX_REQUESTS_PER_HOST = 64;

    /**
     * 默认最大请求数。
     */
    private static final int DEFAULT_MAX_REQUESTS = 128;

    /**
     * 基础 URL。
     */
    private String baseUrl;

    /**
     * 默认请求头。
     */
    private Map<String, String> defaultHeaders = new HashMap<>();

    /**
     * 连接超时。
     */
    private Duration connectTimeout = Duration.ofSeconds(DEFAULT_CONNECT_TIMEOUT_SECONDS);

    /**
     * 读取超时。
     */
    private Duration readTimeout = Duration.ofSeconds(DEFAULT_READ_TIMEOUT_SECONDS);

    /**
     * 每主机最大请求数。
     */
    private int maxRequestsPerHost = DEFAULT_MAX_REQUESTS_PER_HOST;

    /**
     * 最大请求数。
     */
    private int maxRequests = DEFAULT_MAX_REQUESTS;

    /**
     * 是否自动重试连接失败。
     */
    private boolean retryOnConnectionFailure = true;

    /**
     * 是否信任所有证书（不建议在生产环境使用）。
     */
    private boolean trustAllCerts = false;

    /**
     * 默认代理配置（可选）。
     */
    private HttpProxyConfig defaultProxyConfig;

    /*-------------------- getter --------------------*/

    /**
     * 获取基础 URL。
     *
     * @return 基础 URL
     */
    public String getBaseUrl() {
        return baseUrl;
    }

    /*-------------------- setter --------------------*/

    /**
     * 设置基础 URL。
     *
     * @param baseUrl 基础 URL
     * @return 配置对象
     */
    public ApiClientConfig setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
        return this;
    }

    /**
     * 获取默认请求头。
     *
     * @return 默认请求头
     */
    public Map<String, String> getDefaultHeaders() {
        return new HashMap<>(defaultHeaders);
    }

    /**
     * 设置默认请求头。
     *
     * @param defaultHeaders 默认请求头
     * @return 配置对象
     */
    public ApiClientConfig setDefaultHeaders(Map<String, String> defaultHeaders) {
        this.defaultHeaders = new HashMap<>(defaultHeaders);
        return this;
    }

    /**
     * 添加默认请求头。
     *
     * @param name 请求头名称
     * @param value 请求头值
     * @return 配置对象
     */
    public ApiClientConfig addDefaultHeader(String name, String value) {
        this.defaultHeaders.put(name, value);
        return this;
    }

    /**
     * 获取连接超时。
     *
     * @return 连接超时
     */
    public Duration getConnectTimeout() {
        return connectTimeout;
    }

    /**
     * 设置连接超时。
     *
     * @param connectTimeout 连接超时
     * @return 配置对象
     */
    public ApiClientConfig setConnectTimeout(Duration connectTimeout) {
        this.connectTimeout = connectTimeout;
        return this;
    }

    /**
     * 获取读取超时。
     *
     * @return 读取超时
     */
    public Duration getReadTimeout() {
        return readTimeout;
    }

    /**
     * 设置读取超时。
     *
     * @param readTimeout 读取超时
     * @return 配置对象
     */
    public ApiClientConfig setReadTimeout(Duration readTimeout) {
        this.readTimeout = readTimeout;
        return this;
    }

    /**
     * 获取每主机最大请求数。
     *
     * @return 每主机最大请求数
     */
    public int getMaxRequestsPerHost() {
        return maxRequestsPerHost;
    }

    /**
     * 设置每主机最大请求数。
     *
     * @param maxRequestsPerHost 每主机最大请求数
     * @return 配置对象
     */
    public ApiClientConfig setMaxRequestsPerHost(int maxRequestsPerHost) {
        this.maxRequestsPerHost = maxRequestsPerHost;
        return this;
    }

    /**
     * 获取最大请求数。
     *
     * @return 最大请求数
     */
    public int getMaxRequests() {
        return maxRequests;
    }

    /**
     * 设置最大请求数。
     *
     * @param maxRequests 最大请求数
     * @return 配置对象
     */
    public ApiClientConfig setMaxRequests(int maxRequests) {
        this.maxRequests = maxRequests;
        return this;
    }

    /**
     * 是否自动重试连接失败。
     *
     * @return 是否自动重试
     */
    public boolean isRetryOnConnectionFailure() {
        return retryOnConnectionFailure;
    }

    /**
     * 设置是否自动重试连接失败。
     *
     * @param retryOnConnectionFailure 是否自动重试
     * @return 配置对象
     */
    public ApiClientConfig setRetryOnConnectionFailure(boolean retryOnConnectionFailure) {
        this.retryOnConnectionFailure = retryOnConnectionFailure;
        return this;
    }

    /**
     * 是否信任所有证书。
     * 注意：在生产环境下不推荐信任所有证书，这可能导致安全风险。
     *
     * @return 是否信任所有证书
     */
    public boolean isTrustAllCerts() {
        return trustAllCerts;
    }

    /**
     * 设置是否信任所有证书。
     * 注意：在生产环境下不推荐信任所有证书，这可能导致安全风险。
     *
     * @param trustAllCerts 是否信任所有证书
     * @return 配置对象
     */
    public ApiClientConfig setTrustAllCerts(boolean trustAllCerts) {
        this.trustAllCerts = trustAllCerts;
        return this;
    }

    /**
     * 获取默认代理配置。
     *
     * @return 默认代理配置，如果没有设置则返回 null。
     */
    public HttpProxyConfig getDefaultProxyConfig() {
        return defaultProxyConfig;
    }

    /**
     * 设置默认代理配置。
     * <p>当请求没有指定代理时，将使用此默认代理配置。
     *
     * @param defaultProxyConfig 默认代理配置
     * @return 配置对象
     */
    public ApiClientConfig setDefaultProxyConfig(HttpProxyConfig defaultProxyConfig) {
        this.defaultProxyConfig = defaultProxyConfig;
        return this;
    }

    /**
     * 设置默认 HTTP 代理。
     *
     * @param host 代理服务器主机地址
     * @param port 代理服务器端口
     * @return 配置对象
     */
    public ApiClientConfig setDefaultHttpProxy(String host, int port) {
        this.defaultProxyConfig = HttpProxyConfig.http(host, port);
        return this;
    }

    /**
     * 设置默认 HTTP 代理（带认证）。
     *
     * @param host 代理服务器主机地址
     * @param port 代理服务器端口
     * @param username 代理认证用户名
     * @param password 代理认证密码
     * @return 配置对象
     */
    public ApiClientConfig setDefaultHttpProxy(String host, int port, String username, String password) {
        this.defaultProxyConfig = HttpProxyConfig.http(host, port, username, password);
        return this;
    }

    /**
     * 设置默认 SOCKS 代理。
     *
     * @param host 代理服务器主机地址
     * @param port 代理服务器端口
     * @return 配置对象
     */
    public ApiClientConfig setDefaultSocksProxy(String host, int port) {
        this.defaultProxyConfig = HttpProxyConfig.socks(host, port);
        return this;
    }

    /**
     * 设置默认 SOCKS 代理（带认证）。
     *
     * @param host 代理服务器主机地址
     * @param port 代理服务器端口
     * @param username 代理认证用户名
     * @param password 代理认证密码
     * @return 配置对象
     */
    public ApiClientConfig setDefaultSocksProxy(String host, int port, String username, String password) {
        this.defaultProxyConfig = HttpProxyConfig.socks(host, port, username, password);
        return this;
    }

}