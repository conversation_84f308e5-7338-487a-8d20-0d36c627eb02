package cn.bluesking.nanfeng.tools.config;

import jakarta.annotation.Nonnull;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.CacheControl;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.reactive.config.CorsRegistry;
import org.springframework.web.reactive.config.WebFluxConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Web 配置类，用于配置 SSE 和其他 Web 相关设置。
 * <p>提供 WebFlux 和 WebMvc 的配置，包括 CORS 支持、静态资源处理和 JSON 消息转换。
 *
 * <AUTHOR>
 * @date 2025-05-12
 * @since 2.1.5
 */
@Configuration
public class WebConfig {

    /*-------------------- public method --------------------*/

    /**
     * WebFlux 配置，用于配置 SSE 和 CORS。
     * <p>配置了请求内存缓冲区大小和跨域资源共享规则。
     *
     * @return WebFlux 配置器
     */
    @Bean
    public WebFluxConfigurer webFluxConfigurer() {
        return new WebFluxConfigurer() {
            @Override
            public void configureHttpMessageCodecs(@Nonnull ServerCodecConfigurer configurer) {
                configurer.defaultCodecs().maxInMemorySize(16 * 1024 * 1024); // 16MB 缓冲区大小
            }

            @Override
            public void addCorsMappings(@Nonnull CorsRegistry registry) {
                registry.addMapping("/outlook/emailInfo/progress/**")
                        .allowedOrigins("*")
                        .allowedMethods("GET")
                        .maxAge(3600);
            }

        };
    }

    /**
     * WebMvc 配置，用于配置静态资源。
     * <p>配置了静态资源处理和跨域资源共享规则。
     *
     * @return WebMvc 配置器
     */
    @Bean
    public WebMvcConfigurer webMvcConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addResourceHandlers(@Nonnull ResourceHandlerRegistry registry) {
                registry.addResourceHandler("/outlook/emailInfo/progress/**")
                        .setCacheControl(CacheControl.noCache());
            }

            @Override
            public void addCorsMappings(@Nonnull org.springframework.web.servlet.config.annotation.CorsRegistry registry) {
                registry.addMapping("/outlook/emailInfo/progress/**")
                        .allowedOrigins("*")
                        .allowedMethods("GET")
                        .maxAge(3600);
            }

        };
    }

    /**
     * 配置 Jackson HTTP 消息转换器。
     * <p>使用自定义的 ObjectMapper 配置 HTTP 消息转换。
     *
     * @param objectMapper Jackson 对象映射器
     * @return HTTP 消息转换器
     */
    @Bean
    public MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter(ObjectMapper objectMapper) {
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setObjectMapper(objectMapper);
        return converter;
    }

}