package cn.bluesking.nanfeng.tools.config;

import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Jasypt 加密配置类。
 * <p>用于配置 Jasypt 加密器，提供系统中的敏感信息加密需求。
 *
 * <AUTHOR>
 * @date 2025-05-12
 * @since 2.1.5
 */
@Configuration
public class JasyptConfig {

    /*-------------------- public method --------------------*/

    /**
     * 配置 Jasypt 字符串加密器。
     *
     * @return 字符串加密器
     */
    @Bean("jasyptStringEncryptor")
    public StringEncryptor stringEncryptor() {

        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();

        // 从系统属性获取密钥，如果未设置则抛出异常。
        // 生产环境中应通过 -Djasypt.encryptor.password=your_password 传入
        config.setPassword(getEncryptorPassword());
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.NoIvGenerator");
        config.setStringOutputType("base64");

        encryptor.setConfig(config);

        return encryptor;
    }

    /*-------------------- private method --------------------*/

    /**
     * 获取加密器密钥。
     * <p>首先从系统属性获取，如果未找到则从环境变量获取，如果都未设置则抛出异常。
     *
     * @return 加密密钥
     * @throws IllegalStateException 当密钥未设置时抛出
     */
    private String getEncryptorPassword() {
        String password = System.getProperty("jasypt.encryptor.password");
        if (password == null) {
            password = System.getenv("JASYPT_ENCRYPTOR_PASSWORD");
        }

        if (password == null) {
            throw new IllegalStateException("Jasypt 加密密钥未设置!");
        }

        return password;
    }

}