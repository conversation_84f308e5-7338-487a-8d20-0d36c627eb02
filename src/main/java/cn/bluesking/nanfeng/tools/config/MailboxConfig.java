package cn.bluesking.nanfeng.tools.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import cn.bluesking.nanfeng.tools.config.properties.PosteioApiProperties;

/**
 * 邮箱模块相关配置类。
 *
 * <AUTHOR>
 * @date 2025-05-16
 * @since 2.1.10
 */
@Configuration
public class MailboxConfig {

    /*-------------------- public method --------------------*/

    @Bean("mailboxPosteioApiProperties")
    @ConfigurationProperties("mailbox.posteio.api")
    public PosteioApiProperties mailboxPosteioApiProperties() {
        return new PosteioApiProperties();
    }

    /*-------------------- private method --------------------*/

}