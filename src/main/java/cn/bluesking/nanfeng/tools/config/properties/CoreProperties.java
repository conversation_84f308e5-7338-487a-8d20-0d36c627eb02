package cn.bluesking.nanfeng.tools.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 核心配置属性类。
 *
 * <AUTHOR>
 * @date 2025-05-16
 * @since 2.1.7
 */
@Component
@ConfigurationProperties(prefix = "core")
public class CoreProperties {

    /**
     * 服务器 URL，用于构建完整的访问链接。 例如：http://example.com 或 https://api.example.com:8443。
     */
    private String serverUrl;

    /*-------------------- getter --------------------*/

    /**
     * 获取服务器 URL。
     *
     * @return 服务器 URL。
     */
    public String getServerUrl() {
        return serverUrl;
    }

    /*-------------------- setter --------------------*/

    /**
     * 设置服务器 URL。
     *
     * @param serverUrl 服务器 URL。
     */
    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "{\"CoreProperties\":{" +
                   "\"serverUrl\": \"" + serverUrl + '\"' +
                   "}}";
    }

}