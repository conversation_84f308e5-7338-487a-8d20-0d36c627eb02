package cn.bluesking.nanfeng.tools.config.properties;

import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Public 数据源的 JPA 配置属性。
 *
 * <p>支持标准的 Spring Boot JPA 配置项，同时提供 public 数据源特定的配置覆盖。
 * <p>这种方式避免了硬编码，充分利用了 Spring Boot 的配置绑定机制。
 *
 * <AUTHOR>
 * @date 2025-06-19
 * @since 2.2.0
 */
@Component
@ConfigurationProperties(prefix = "spring.jpa.public")
public class PublicJpaProperties {

    /**
     * 是否生成 DDL。
     */
    private Boolean generateDdl;

    /**
     * 是否显示 SQL。
     */
    private Boolean showSql;

    /**
     * 数据库平台/方言。
     */
    private String databasePlatform;

    /**
     * Hibernate 特定配置。
     */
    private Hibernate hibernate = new Hibernate();

    /**
     * 自定义属性，对应 spring.jpa.public.properties.* 配置。
     */
    private Map<String, String> properties = new HashMap<>();

    /*-------------------- getter and setter --------------------*/

    public Boolean getGenerateDdl() {
        return generateDdl;
    }

    public void setGenerateDdl(Boolean generateDdl) {
        this.generateDdl = generateDdl;
    }

    public Boolean getShowSql() {
        return showSql;
    }

    public void setShowSql(Boolean showSql) {
        this.showSql = showSql;
    }

    public String getDatabasePlatform() {
        return databasePlatform;
    }

    public void setDatabasePlatform(String databasePlatform) {
        this.databasePlatform = databasePlatform;
    }

    public Hibernate getHibernate() {
        return hibernate;
    }

    public void setHibernate(Hibernate hibernate) {
        this.hibernate = hibernate;
    }

    public Map<String, String> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, String> properties) {
        this.properties = properties;
    }

    /*-------------------- inner class --------------------*/

    /**
     * Hibernate 特定配置。
     */
    public static class Hibernate {

        /**
         * DDL 自动生成策略。
         */
        private String ddlAuto;

        /**
         * 数据库方言。
         */
        private String dialect;

        /**
         * 是否显示 SQL。
         */
        private String showSql;

        /**
         * 是否格式化 SQL。
         */
        private String formatSql;

        /**
         * 命名策略配置。
         */
        private Naming naming = new Naming();

        /**
         * 连接配置。
         */
        private Connection connection = new Connection();

        /**
         * JDBC 配置。
         */
        private Jdbc jdbc = new Jdbc();

        /**
         * 缓存配置。
         */
        private Cache cache = new Cache();

        /*-------------------- getter and setter --------------------*/

        public String getDdlAuto() {
            return ddlAuto;
        }

        public void setDdlAuto(String ddlAuto) {
            this.ddlAuto = ddlAuto;
        }

        public String getDialect() {
            return dialect;
        }

        public void setDialect(String dialect) {
            this.dialect = dialect;
        }

        public String getShowSql() {
            return showSql;
        }

        public void setShowSql(String showSql) {
            this.showSql = showSql;
        }

        public String getFormatSql() {
            return formatSql;
        }

        public void setFormatSql(String formatSql) {
            this.formatSql = formatSql;
        }

        public Naming getNaming() {
            return naming;
        }

        public void setNaming(Naming naming) {
            this.naming = naming;
        }

        public Connection getConnection() {
            return connection;
        }

        public void setConnection(Connection connection) {
            this.connection = connection;
        }

        public Jdbc getJdbc() {
            return jdbc;
        }

        public void setJdbc(Jdbc jdbc) {
            this.jdbc = jdbc;
        }

        public Cache getCache() {
            return cache;
        }

        public void setCache(Cache cache) {
            this.cache = cache;
        }

        /*-------------------- inner class --------------------*/

        /**
         * 命名策略配置。
         */
        public static class Naming {

            private String physicalStrategy;
            private String implicitStrategy;

            public String getPhysicalStrategy() {
                return physicalStrategy;
            }

            public void setPhysicalStrategy(String physicalStrategy) {
                this.physicalStrategy = physicalStrategy;
            }

            public String getImplicitStrategy() {
                return implicitStrategy;
            }

            public void setImplicitStrategy(String implicitStrategy) {
                this.implicitStrategy = implicitStrategy;
            }
        }

        /**
         * 连接配置。
         */
        public static class Connection {

            private String poolSize;

            public String getPoolSize() {
                return poolSize;
            }

            public void setPoolSize(String poolSize) {
                this.poolSize = poolSize;
            }
        }

        /**
         * JDBC 配置。
         */
        public static class Jdbc {

            private String batchSize;
            private String fetchSize;

            public String getBatchSize() {
                return batchSize;
            }

            public void setBatchSize(String batchSize) {
                this.batchSize = batchSize;
            }

            public String getFetchSize() {
                return fetchSize;
            }

            public void setFetchSize(String fetchSize) {
                this.fetchSize = fetchSize;
            }
        }

        /**
         * 缓存配置。
         */
        public static class Cache {

            private String useSecondLevelCache;
            private String useQueryCache;

            public String getUseSecondLevelCache() {
                return useSecondLevelCache;
            }

            public void setUseSecondLevelCache(String useSecondLevelCache) {
                this.useSecondLevelCache = useSecondLevelCache;
            }

            public String getUseQueryCache() {
                return useQueryCache;
            }

            public void setUseQueryCache(String useQueryCache) {
                this.useQueryCache = useQueryCache;
            }
        }
    }
}