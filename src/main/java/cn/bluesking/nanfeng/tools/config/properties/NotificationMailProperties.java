package cn.bluesking.nanfeng.tools.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 邮件通知配置属性类。
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "notification.mail")
public class NotificationMailProperties {

    /**
     * 邮件服务器主机地址。
     */
    private String host;

    /**
     * 邮件服务器端口。
     */
    private int port = 25;

    /**
     * 邮件服务器用户名。
     */
    private String username;

    /**
     * 邮件服务器密码。
     */
    private String password;

    /**
     * 默认发件人邮箱地址。
     */
    private String from;

    /**
     * 默认发件人显示名称。
     */
    private String fromName;

    /**
     * 是否启用 SSL。
     */
    private boolean ssl = false;

    /**
     * 是否启用 TLS。
     */
    private boolean tls = false;

    /**
     * 连接超时时间，单位毫秒。
     */
    private int connectionTimeout = 5000;

    /**
     * 读取超时时间，单位毫秒。
     */
    private int timeout = 5000;

    /**
     * 是否启用邮件通知功能。
     */
    private boolean enabled = true;

    /**
     * 邮件模板 LOGO URL。
     */
    private String logoUrl = "https://tools.bluesking.cn/assets/images/logo.png";

    /*-------------------- getter --------------------*/

    /**
     * 获取邮件服务器主机地址。
     *
     * @return 邮件服务器主机地址
     */
    public String getHost() {
        return host;
    }

    /**
     * 获取邮件服务器端口。
     *
     * @return 邮件服务器端口
     */
    public int getPort() {
        return port;
    }

    /**
     * 获取邮件服务器用户名。
     *
     * @return 邮件服务器用户名
     */
    public String getUsername() {
        return username;
    }

    /**
     * 获取邮件服务器密码。
     *
     * @return 邮件服务器密码
     */
    public String getPassword() {
        return password;
    }

    /**
     * 获取默认发件人邮箱地址。
     *
     * @return 默认发件人邮箱地址
     */
    public String getFrom() {
        return from;
    }

    /**
     * 获取默认发件人显示名称。
     *
     * @return 默认发件人显示名称
     */
    public String getFromName() {
        return fromName;
    }

    /**
     * 是否启用 SSL。
     *
     * @return 是否启用 SSL
     */
    public boolean isSsl() {
        return ssl;
    }

    /**
     * 是否启用 TLS。
     *
     * @return 是否启用 TLS
     */
    public boolean isTls() {
        return tls;
    }

    /**
     * 获取连接超时时间，单位毫秒。
     *
     * @return 连接超时时间
     */
    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    /**
     * 获取读取超时时间，单位毫秒。
     *
     * @return 读取超时时间
     */
    public int getTimeout() {
        return timeout;
    }

    /**
     * 是否启用邮件通知功能。
     *
     * @return 是否启用邮件通知功能
     */
    public boolean isEnabled() {
        return enabled;
    }

    /**
     * 获取邮件模板 LOGO URL。
     *
     * @return 邮件模板 LOGO URL
     */
    public String getLogoUrl() {
        return logoUrl;
    }

    /*-------------------- setter --------------------*/

    /**
     * 设置邮件服务器主机地址。
     *
     * @param host 邮件服务器主机地址
     */
    public void setHost(String host) {
        this.host = host;
    }

    /**
     * 设置邮件服务器端口。
     *
     * @param port 邮件服务器端口
     */
    public void setPort(int port) {
        this.port = port;
    }

    /**
     * 设置邮件服务器用户名。
     *
     * @param username 邮件服务器用户名
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * 设置邮件服务器密码。
     *
     * @param password 邮件服务器密码
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * 设置默认发件人邮箱地址。
     *
     * @param from 默认发件人邮箱地址
     */
    public void setFrom(String from) {
        this.from = from;
    }

    /**
     * 设置默认发件人显示名称。
     *
     * @param fromName 默认发件人显示名称
     */
    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    /**
     * 设置是否启用 SSL。
     *
     * @param ssl 是否启用 SSL
     */
    public void setSsl(boolean ssl) {
        this.ssl = ssl;
    }

    /**
     * 设置是否启用 TLS。
     *
     * @param tls 是否启用 TLS
     */
    public void setTls(boolean tls) {
        this.tls = tls;
    }

    /**
     * 设置连接超时时间，单位毫秒。
     *
     * @param connectionTimeout 连接超时时间
     */
    public void setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }

    /**
     * 设置读取超时时间，单位毫秒。
     *
     * @param timeout 读取超时时间
     */
    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }

    /**
     * 设置是否启用邮件通知功能。
     *
     * @param enabled 是否启用邮件通知功能
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    /**
     * 设置邮件模板 LOGO URL。
     *
     * @param logoUrl 邮件模板 LOGO URL
     */
    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "{\"NotificationMailProperties\":{" +
                   "\"host\": \"" + host + '\"' +
                   ", \"port\": " + port +
                   ", \"username\": \"" + username + '\"' +
                   ", \"password\": \"" + password + '\"' +
                   ", \"from\": \"" + from + '\"' +
                   ", \"fromName\": \"" + fromName + '\"' +
                   ", \"ssl\": " + ssl +
                   ", \"tls\": " + tls +
                   ", \"connectionTimeout\": " + connectionTimeout +
                   ", \"timeout\": " + timeout +
                   ", \"enabled\": " + enabled +
                   ", \"logoUrl\": \"" + logoUrl + '\"' +
                   "}}";
    }

}