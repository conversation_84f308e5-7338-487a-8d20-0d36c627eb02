package cn.bluesking.nanfeng.tools.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Posteio API 的配置信息。
 *
 * <AUTHOR>
 */
public class PosteioApiProperties {

    /**
     * API 基础 URL。
     */
    private String baseUrl;

    /**
     * 电子邮件后缀。
     */
    private String emailSuffix;

    /**
     * 用户名。
     */
    private String username;

    /**
     * 密码。
     */
    private String password;

    /*-------------------- getter --------------------*/

    /**
     * 获取 API 基础 URL。
     *
     * @return API 基础 URL。
     */
    public String getBaseUrl() {
        return baseUrl;
    }

    /**
     * 获取电子邮件后缀。
     *
     * @return 电子邮件后缀。
     */
    public String getEmailSuffix() {
        return emailSuffix;
    }

    /**
     * 获取用户名。
     *
     * @return 用户名。
     */
    public String getUsername() {
        return username;
    }

    /**
     * 获取密码。
     *
     * @return 密码。
     */
    public String getPassword() {
        return password;
    }

    /*-------------------- setter --------------------*/

    /**
     * 设置 API 基础 URL。
     *
     * @param baseUrl API 基础 URL。
     */
    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    /**
     * 设置电子邮件后缀。
     *
     * @param emailSuffix 电子邮件后缀。
     */
    public void setEmailSuffix(String emailSuffix) {
        this.emailSuffix = emailSuffix;
    }

    /**
     * 设置用户名。
     *
     * @param username 用户名。
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * 设置密码。
     *
     * @param password 密码。
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "{\"PosteioApiProperties\":{" +
                   "\"baseUrl\": \"" + baseUrl + '\"' +
                   ", \"emailSuffix\": \"" + emailSuffix + '\"' +
                   ", \"username\": \"" + username + '\"' +
                   ", \"password\": \"" + password + '\"' +
                   "}}";
    }

}