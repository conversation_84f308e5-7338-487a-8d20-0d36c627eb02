package cn.bluesking.nanfeng.tools.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 异步任务配置类。
 * <p>为系统提供异步任务执行能力，配置异步任务线程池参数。
 *
 * <AUTHOR>
 * @date 2025-05-12
 * @since 2.1.5
 */
@EnableAsync
@Configuration
public class AsyncConfig {

    private static final Logger logger = LoggerFactory.getLogger(AsyncConfig.class);

    /**
     * 线程池核心线程数。
     */
    private static final int CORE_POOL_SIZE = 5;

    /**
     * 线程池最大线程数。
     */
    private static final int MAX_POOL_SIZE = 10;

    /**
     * 线程池队列容量。
     */
    private static final int QUEUE_CAPACITY = 50;

    /**
     * 线程空闲时间（秒）。
     */
    private static final int KEEP_ALIVE_SECONDS = 60;

    /**
     * 线程名称前缀。
     */
    private static final String THREAD_NAME_PREFIX = "Async-Task-";

    /*-------------------- public method --------------------*/

    /**
     * 创建异步任务执行器。
     * <p>配置线程池参数，使用 CallerRunsPolicy 作为拒绝策略，避免任务丢失。
     *
     * @return 异步任务执行器
     */
    @Bean("taskExecutor")
    public Executor taskExecutor() {

        logger.info("initializing task executor");

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(CORE_POOL_SIZE);
        executor.setMaxPoolSize(MAX_POOL_SIZE);
        executor.setQueueCapacity(QUEUE_CAPACITY);
        executor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);
        executor.setThreadNamePrefix(THREAD_NAME_PREFIX);

        // 拒绝策略：调用者线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 初始化
        executor.initialize();

        return executor;
    }

}