package cn.bluesking.nanfeng.tools.config;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.TimeZone;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;

import cn.bluesking.nanfeng.tools.common.constant.DateTimeConstants;

/**
 * Jackson 配置类，用于设置 JSON 序列化的全局配置。
 * <p>定制 ObjectMapper，统一配置日期时间格式化规则，支持 Java 8 日期时间类型。
 *
 * <AUTHOR>
 * @date 2025-05-12
 * @since 2.1.5
 */
@Configuration
public class JacksonConfig {

    /*-------------------- public method --------------------*/

    /**
     * 配置 ObjectMapper。
     * <p>自定义日期格式、时区设置，并为 Java 8 的日期时间类型注册序列化和反序列化器。
     *
     * @return 定制的 ObjectMapper
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {

        ObjectMapper objectMapper = new ObjectMapper();

        // 设置日期格式
        objectMapper.setDateFormat(new SimpleDateFormat(DateTimeConstants.DEFAULT_DATE_TIME_FORMAT));

        // 设置时区
        objectMapper.setTimeZone(TimeZone.getDefault());

        // 注册 Java 8 时间模块
        JavaTimeModule javaTimeModule = new JavaTimeModule();

        // 创建格式化器
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(DateTimeConstants.DEFAULT_DATE_FORMAT);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateTimeConstants.DEFAULT_DATE_TIME_FORMAT);
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateTimeConstants.DEFAULT_TIME_FORMAT);

        // 配置 LocalDate 序列化和反序列化
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(dateFormatter));
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(dateFormatter));

        // 配置 LocalDateTime 序列化和反序列化
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateTimeFormatter));

        // 配置 LocalTime 序列化和反序列化
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(timeFormatter));
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(timeFormatter));

        objectMapper.registerModule(javaTimeModule);

        // 禁用将日期写为时间戳
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        return objectMapper;
    }

}