package cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration;

import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto.IpLocationInfo;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto.LocationInfo;

import reactor.core.publisher.Mono;

/**
 * 地理位置 API 适配器接口，定义了不同平台的地理位置 API 服务的统一接口。
 *
 * <AUTHOR>
 * @date 2025-05-15
 * @since 2.1.6
 */
public interface GeoApiAdapter {

    /**
     * 获取适配器对应的平台。
     *
     * @return 平台枚举
     */
    Platform getPlatform();

    /**
     * 根据 IP 获取位置信息。 默认返回不支持此操作的异常。
     *
     * @param ip IP 地址
     * @return 位置信息
     */
    default Mono<IpLocationInfo> getLocationByIp(String ip) {
        return Mono.error(new UnsupportedOperationException(getPlatform() + " 不支持 IP 地址定位查询"));
    }

    /**
     * 根据经纬度获取地址信息。 默认返回不支持此操作的异常。
     *
     * @param latitude 纬度
     * @param longitude 经度
     * @return 地址信息
     */
    default Mono<LocationInfo> getAddressByLocation(Double latitude, Double longitude) {
        return Mono.error(new UnsupportedOperationException(getPlatform() + " 不支持基于经纬度的地址查询"));
    }

}