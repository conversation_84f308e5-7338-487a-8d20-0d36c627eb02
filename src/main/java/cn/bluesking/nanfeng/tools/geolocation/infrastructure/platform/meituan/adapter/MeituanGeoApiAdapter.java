package cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.meituan.adapter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.GeoApiAdapter;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.Platform;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto.IpLocationInfo;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto.LocationInfo;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.meituan.MeituanApi;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.meituan.dto.CityDetailResponse;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.meituan.dto.IpLocationResponse;

import reactor.core.publisher.Mono;

/**
 * 美团地理位置 API 适配器实现类。
 *
 * <AUTHOR>
 */
@Component
class MeituanGeoApiAdapter implements GeoApiAdapter {

    /**
     * 日志记录器。
     */
    private static final Logger logger = LoggerFactory.getLogger(MeituanGeoApiAdapter.class);

    /**
     * 美团 API 接口。
     */
    private final MeituanApi meituanApi;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数。
     *
     * @param meituanApi 美团 API 接口
     */
    MeituanGeoApiAdapter(MeituanApi meituanApi) {
        this.meituanApi = meituanApi;
    }

    /*-------------------- public method --------------------*/

    @Override
    public Platform getPlatform() {
        return Platform.MEITUAN;
    }

    @Override
    public Mono<IpLocationInfo> getLocationByIp(String ip) {
        return meituanApi.getLocationByIp(ip)
                   .map(this::convertToIpLocationInfo)
                   .onErrorResume(e -> {
                       logger.warn("failed to get location from Meituan API.", e);
                       return Mono.empty();
                   });
    }

    @Override
    public Mono<LocationInfo> getAddressByLocation(Double latitude, Double longitude) {
        return meituanApi.getCityDetailByLocation(latitude, longitude)
                   .map(this::convertToLocationInfo)
                   .onErrorResume(e -> {
                       logger.warn("failed to get address from Meituan API.", e);
                       return Mono.empty();
                   });
    }

    /*-------------------- private method --------------------*/

    /**
     * 将美团 IP 位置响应转换为统一的 IP 位置信息。
     *
     * @param response 美团 IP 位置响应
     * @return IP 位置信息
     */
    private IpLocationInfo convertToIpLocationInfo(IpLocationResponse response) {

        IpLocationInfo ipLocationInfo = new IpLocationInfo();

        IpLocationResponse.LocationData data = response.getData();
        if (data != null) {

            ipLocationInfo.setLatitude(data.getLat());
            ipLocationInfo.setLongitude(data.getLng());

            IpLocationResponse.RgeoInfo rgeo = data.getRgeo();
            if (rgeo != null) {

                ipLocationInfo.setCountry(rgeo.getCountry());
                ipLocationInfo.setProvince(rgeo.getProvince());
                ipLocationInfo.setCity(rgeo.getCity());
                ipLocationInfo.setDistrict(rgeo.getDistrict());
            }

        }

        return ipLocationInfo;
    }

    /**
     * 将美团城市详情响应转换为统一的位置信息。
     *
     * @param response 美团城市详情响应
     * @return 位置信息
     */
    private LocationInfo convertToLocationInfo(CityDetailResponse response) {

        LocationInfo locationInfo = new LocationInfo();

        CityDetailResponse.CityData data = response.getData();
        if (data != null) {

            locationInfo.setFormattedAddress(data.getDetail());
            locationInfo.setProvince(data.getProvince());
            locationInfo.setCity(data.getCity());
            locationInfo.setDistrict(data.getDistrict());
            locationInfo.setAreaName(data.getAreaName());
        }

        return locationInfo;
    }

}