package cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.baidu;

import cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.baidu.dto.BaiduMapResponse;

import reactor.core.publisher.Mono;

/**
 * 百度地图 API 接口，提供百度地图地理位置相关服务调用。
 *
 * <AUTHOR>
 * @date 2025-05-15
 * @since 2.1.6
 */
public interface BaiduMapApi {

    /**
     * 根据经纬度查询详细地址信息。
     *
     * @param lat 纬度
     * @param lng 经度
     * @return 包含详细地址信息的响应
     */
    Mono<BaiduMapResponse> getAddressByLocation(Double lat, Double lng);

}