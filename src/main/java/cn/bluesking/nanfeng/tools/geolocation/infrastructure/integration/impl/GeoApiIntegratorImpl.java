package cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.impl;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.GeoApiAdapter;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.GeoApiIntegrator;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.Platform;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto.AddressDetail;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto.AddressDetail.SourceAddressDetail;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto.IpLocationInfo;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto.LocationInfo;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 地理位置 API 集成器实现类。
 *
 * <AUTHOR>
 * @date 2025-05-15
 * @since 2.1.6
 */
@Service
class GeoApiIntegratorImpl implements GeoApiIntegrator {

    /**
     * 日志记录器。
     */
    private static final Logger logger = LoggerFactory.getLogger(GeoApiIntegratorImpl.class);

    /**
     * 适配器映射表。
     */
    private final Map<Platform, GeoApiAdapter> adapterMap;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数，通过依赖注入适配器列表。
     *
     * @param adapters 适配器列表
     */
    GeoApiIntegratorImpl(List<GeoApiAdapter> adapters) {
        this.adapterMap = adapters.stream()
                              .collect(Collectors.toMap(GeoApiAdapter::getPlatform, adapter -> adapter));
    }

    /*-------------------- public method --------------------*/

    /**
     * 获取指定平台的适配器。
     *
     * @param platform 平台
     * @return 对应平台的适配器，若不存在则返回 null。
     */
    @Override
    public GeoApiAdapter getAdapter(Platform platform) {
        return adapterMap.getOrDefault(platform, null);
    }

    /**
     * 根据 IP 获取地址详情。
     *
     * @param ip IP 地址
     * @return 包含基础地址详情及各平台信息的 Mono
     */
    @Override
    public Mono<AddressDetail> getAddressDetailByIp(String ip) {
        GeoApiAdapter meituanAdapter = getAdapter(Platform.MEITUAN);
        if (meituanAdapter == null) {
            return Mono.error(new IllegalStateException("未找到 Meituan 适配器"));
        }

        return meituanAdapter.getLocationByIp(ip)
                   .flatMap(ipLocationInfo -> {
                       AddressDetail addressDetail = createBaseAddressDetail(ip, ipLocationInfo);
                       return getLocationInfoByCoordinates(ipLocationInfo.getLatitude(), ipLocationInfo.getLongitude())
                                  .map(platformLocationMap -> enrichAddressDetail(addressDetail, platformLocationMap));
                   });
    }

    /**
     * 根据经纬度获取各平台位置信息映射。
     *
     * @param latitude 纬度
     * @param longitude 经度
     * @return 平台与位置信息的映射 Mono
     */
    @Override
    public Mono<Map<Platform, LocationInfo>> getLocationInfoByCoordinates(Double latitude, Double longitude) {

        List<Mono<Map.Entry<Platform, LocationInfo>>> locationMonos =
            adapterMap.values().stream()
                .map(adapter -> getAddressByLocationQuietly(latitude, longitude, adapter))
                .collect(Collectors.toList());

        return Flux.merge(locationMonos)
                   .collectMap(Map.Entry::getKey, Map.Entry::getValue);
    }

    /*-------------------- private method --------------------*/

    private Mono<Entry<Platform, LocationInfo>> getAddressByLocationQuietly(Double latitude,
                                                                            Double longitude,
                                                                            GeoApiAdapter adapter) {

        return adapter.getAddressByLocation(latitude, longitude)
                   .map(locationInfo -> Map.entry(adapter.getPlatform(), locationInfo))
                   .onErrorResume(e -> {
                       logger.warn("error getting location from {}: {}", adapter.getPlatform(),
                           e.getMessage());
                       return Mono.empty();
                   });
    }

    /**
     * 创建基础地址详情对象。
     *
     * @param ip IP 地址
     * @param ipLocationInfo IP 位置信息
     * @return 地址详情对象
     */
    private AddressDetail createBaseAddressDetail(String ip, IpLocationInfo ipLocationInfo) {

        AddressDetail addressDetail = new AddressDetail();

        addressDetail.setIp(ip);
        addressDetail.setLatitude(ipLocationInfo.getLatitude());
        addressDetail.setLongitude(ipLocationInfo.getLongitude());
        addressDetail.setCountry(ipLocationInfo.getCountry());
        addressDetail.setProvince(ipLocationInfo.getProvince());
        addressDetail.setCity(ipLocationInfo.getCity());
        addressDetail.setDistrict(ipLocationInfo.getDistrict());

        return addressDetail;
    }

    /**
     * 使用各平台位置信息丰富地址详情。
     *
     * @param addressDetail 地址详情对象
     * @param platformLocationMap 平台位置信息映射
     * @return 丰富后的地址详情对象
     */
    private AddressDetail enrichAddressDetail(AddressDetail addressDetail,
                                              Map<Platform, LocationInfo> platformLocationMap) {

        platformLocationMap.forEach((platform, locationInfo) -> {

            SourceAddressDetail sourceDetail = convertToSourceDetail(locationInfo);
            addressDetail.addSourceDetail(platform.name().toLowerCase(), sourceDetail);

            if (platform == Platform.MEITUAN) {

                addressDetail.setDetail(locationInfo.getFormattedAddress());
                addressDetail.setAreaName(locationInfo.getAreaName());
            }

        });

        return addressDetail;
    }

    /**
     * 将 LocationInfo 转换为 SourceAddressDetail。
     *
     * @param locationInfo 位置信息
     * @return 源地址详情
     */
    private SourceAddressDetail convertToSourceDetail(LocationInfo locationInfo) {

        SourceAddressDetail sourceDetail = new SourceAddressDetail();

        sourceDetail.setFormattedAddress(locationInfo.getFormattedAddress());
        sourceDetail.setCountry(locationInfo.getCountry());
        sourceDetail.setProvince(locationInfo.getProvince());
        sourceDetail.setCity(locationInfo.getCity());
        sourceDetail.setDistrict(locationInfo.getDistrict());
        sourceDetail.setStreet(locationInfo.getStreet());

        locationInfo.getExtraInfo().forEach(sourceDetail::addExtraInfo);

        return sourceDetail;
    }

}