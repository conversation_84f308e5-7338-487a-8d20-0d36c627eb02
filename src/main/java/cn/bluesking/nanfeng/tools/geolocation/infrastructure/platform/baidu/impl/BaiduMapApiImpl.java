package cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.baidu.impl;

import cn.bluesking.nanfeng.tools.common.http.ApiClient;
import cn.bluesking.nanfeng.tools.common.http.ApiClientFactory;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.baidu.BaiduMapApi;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.baidu.dto.BaiduMapResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 百度地图 API 接口实现类。
 *
 * <AUTHOR>
 * @date 2025-05-15
 * @since 2.1.6
 */
@Service
class BaiduMapApiImpl implements BaiduMapApi {

    /**
     * API 基础 URL。
     */
    private static final String BASE_URL = "https://api.map.baidu.com";

    /**
     * 百度地图 API 密钥。
     */
    @Value("${baidu.map.ak}")
    private String ak;

    /**
     * API 客户端。
     */
    private final ApiClient apiClient;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数。
     */
    public BaiduMapApiImpl() {
        this.apiClient = ApiClientFactory.builder()
                             .baseUrl(BASE_URL)
                             .build();
    }

    /*-------------------- public method --------------------*/

    @Override
    public Mono<BaiduMapResponse> getAddressByLocation(Double lat, Double lng) {

        // 构建请求参数
        String location = String.format("%s,%s", lat, lng);
        String encodedLocation = URLEncoder.encode(location, StandardCharsets.UTF_8);

        // 构建完整 URL
        String url = String.format(
            "/reverse_geocoding/v3/?ak=%s&output=json&coordtype=bd09ll&location=%s&entire_poi=1&sort_strategy=distance",
            ak, encodedLocation);

        // 发送请求
        return apiClient.request()
                .uri(url)
                .get()
                .retrieveJson(BaiduMapResponse.class);
    }

}