package cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.meituan;

import cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.meituan.dto.CityDetailResponse;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.meituan.dto.IpLocationResponse;
import reactor.core.publisher.Mono;

/**
 * 美团 API 接口，提供美团地理位置相关服务调用。
 *
 * <AUTHOR>
 * @date 2025-05-15
 * @since 2.1.6
 */
public interface MeituanApi {

    /**
     * 根据 IP 查询经纬度和大致位置信息。
     *
     * @param ip IP 地址
     * @return 包含位置信息的响应
     */
    Mono<IpLocationResponse> getLocationByIp(String ip);

    /**
     * 根据经纬度查询详细地址信息。
     *
     * @param lat 纬度
     * @param lng 经度
     * @return 包含详细地址信息的响应
     */
    Mono<CityDetailResponse> getCityDetailByLocation(Double lat, Double lng);

}