package cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto;

import java.util.HashMap;
import java.util.Map;

/**
 * 位置信息数据传输对象，用于表示从不同平台获取的位置信息。
 *
 * <AUTHOR>
 * @date 2025-05-15
 * @since 2.1.6
 */
public class LocationInfo {

    /**
     * 格式化的地址信息。
     */
    private String formattedAddress;

    /**
     * 国家。
     */
    private String country;

    /**
     * 省份。
     */
    private String province;

    /**
     * 城市。
     */
    private String city;

    /**
     * 区县。
     */
    private String district;

    /**
     * 街道。
     */
    private String street;

    /**
     * 商圈名称。
     */
    private String areaName;

    /**
     * 额外信息。
     */
    private final Map<String, String> extraInfo = new HashMap<>();

    /*-------------------- getter --------------------*/

    public String getFormattedAddress() {
        return formattedAddress;
    }

    public String getCountry() {
        return country;
    }

    public String getProvince() {
        return province;
    }

    public String getCity() {
        return city;
    }

    public String getDistrict() {
        return district;
    }

    public String getStreet() {
        return street;
    }

    public String getAreaName() {
        return areaName;
    }

    public Map<String, String> getExtraInfo() {
        return extraInfo;
    }

    /*-------------------- setter --------------------*/

    public void setFormattedAddress(String formattedAddress) {
        this.formattedAddress = formattedAddress;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    /*-------------------- public method --------------------*/

    /**
     * 添加额外信息。
     *
     * @param key 键
     * @param value 值
     */
    public void addExtraInfo(String key, String value) {
        if (value != null) {
            this.extraInfo.put(key, value);
        }

    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "{\"LocationInfo\":{" +
                   "\"formattedAddress\": \"" + formattedAddress + '\"' +
                   ", \"country\": \"" + country + '\"' +
                   ", \"province\": \"" + province + '\"' +
                   ", \"city\": \"" + city + '\"' +
                   ", \"district\": \"" + district + '\"' +
                   ", \"street\": \"" + street + '\"' +
                   ", \"areaName\": \"" + areaName + '\"' +
                   ", \"extraInfo\": " + extraInfo +
                   "}}";
    }

}