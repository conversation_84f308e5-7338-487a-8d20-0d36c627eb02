package cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration;

import java.util.Map;

import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto.AddressDetail;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto.LocationInfo;

import reactor.core.publisher.Mono;

/**
 * 地理位置 API 集成器接口，用于整合不同平台的地理位置服务。
 *
 * <AUTHOR>
 * @date 2025-05-15
 * @since 2.1.6
 */
public interface GeoApiIntegrator {

    /**
     * 获取指定平台的适配器。
     *
     * @param platform 平台枚举
     * @return 对应平台的适配器
     */
    GeoApiAdapter getAdapter(Platform platform);

    /**
     * 根据 IP 获取地址详细信息。
     *
     * @param ip IP 地址
     * @return 详细地址信息
     */
    Mono<AddressDetail> getAddressDetailByIp(String ip);

    /**
     * 根据经纬度获取所有平台的位置信息。
     *
     * @param latitude 纬度
     * @param longitude 经度
     * @return 各平台的位置信息映射
     */
    Mono<Map<Platform, LocationInfo>> getLocationInfoByCoordinates(Double latitude, Double longitude);

}