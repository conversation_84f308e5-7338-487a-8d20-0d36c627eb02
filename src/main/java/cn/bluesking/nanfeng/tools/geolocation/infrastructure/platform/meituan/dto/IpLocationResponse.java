package cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.meituan.dto;

import com.google.gson.annotations.SerializedName;

/**
 * IP 定位响应模型。
 *
 * <AUTHOR>
 */
public class IpLocationResponse {

    /**
     * 响应数据
     */
    private LocationData data;

    /*-------------------- getter --------------------*/

    public LocationData getData() {
        return data;
    }

    /*-------------------- setter --------------------*/

    public void setData(LocationData data) {
        this.data = data;
    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "{\"IpLocationResponse\":{" +
                   "\"data\": " + data +
                   "}}";
    }

    /*-------------------- inner class --------------------*/

    /**
     * 定位数据
     */
    public static class LocationData {
        /**
         * 经度
         */
        private Double lng;

        /**
         * 纬度
         */
        private Double lat;

        /**
         * 数据来源
         */
        @SerializedName("fromwhere")
        private String fromwhere;

        /**
         * IP 地址
         */
        private String ip;

        /**
         * 地理编码信息
         */
        private RgeoInfo rgeo;

        /*-------------------- getter --------------------*/

        public Double getLng() {
            return lng;
        }

        public Double getLat() {
            return lat;
        }

        public String getFromwhere() {
            return fromwhere;
        }

        public String getIp() {
            return ip;
        }

        public RgeoInfo getRgeo() {
            return rgeo;
        }

        /*-------------------- setter --------------------*/

        public void setLng(Double lng) {
            this.lng = lng;
        }

        public void setLat(Double lat) {
            this.lat = lat;
        }

        public void setFromwhere(String fromwhere) {
            this.fromwhere = fromwhere;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public void setRgeo(
            RgeoInfo rgeo) {
            this.rgeo = rgeo;
        }

        /*-------------------- toString --------------------*/

        @Override
        public String toString() {
            return "{\"LocationData\":{" +
                       "\"lng\": " + lng +
                       ", \"lat\": " + lat +
                       ", \"fromwhere\": \"" + fromwhere + '\"' +
                       ", \"ip\": \"" + ip + '\"' +
                       ", \"rgeo\": " + rgeo +
                       "}}";
        }

    }

    /**
     * 地理编码信息
     */
    public static class RgeoInfo {
        /**
         * 国家
         */
        private String country;

        /**
         * 省份
         */
        private String province;

        /**
         * 行政区划代码
         */
        private String adcode;

        /**
         * 城市
         */
        private String city;

        /**
         * 区县
         */
        private String district;

        /*-------------------- getter --------------------*/

        public String getCountry() {
            return country;
        }

        public String getProvince() {
            return province;
        }

        public String getAdcode() {
            return adcode;
        }

        public String getCity() {
            return city;
        }

        public String getDistrict() {
            return district;
        }

        /*-------------------- setter --------------------*/

        public void setCountry(String country) {
            this.country = country;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public void setAdcode(String adcode) {
            this.adcode = adcode;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public void setDistrict(String district) {
            this.district = district;
        }

        /*-------------------- toString --------------------*/

        @Override
        public String toString() {
            return "{\"RgeoInfo\":{" +
                       "\"country\": \"" + country + '\"' +
                       ", \"province\": \"" + province + '\"' +
                       ", \"adcode\": \"" + adcode + '\"' +
                       ", \"city\": \"" + city + '\"' +
                       ", \"district\": \"" + district + '\"' +
                       "}}";
        }

    }

}