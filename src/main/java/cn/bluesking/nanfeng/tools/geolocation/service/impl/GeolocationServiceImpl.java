package cn.bluesking.nanfeng.tools.geolocation.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.GeoApiIntegrator;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto.AddressDetail;
import cn.bluesking.nanfeng.tools.geolocation.service.GeolocationService;
import cn.bluesking.nanfeng.tools.geolocation.service.dto.IpAddressQueryParam;

import reactor.core.publisher.Mono;

/**
 * 地理位置服务接口实现类。
 * <p>通过集成器调用多个平台的地理位置服务，提供地址查询功能。
 *
 * <AUTHOR>
 * @date 2025-05-15
 * @since 2.1.6
 */
@Service
class GeolocationServiceImpl implements GeolocationService {

    /**
     * 日志记录器。
     */
    private static final Logger logger = LoggerFactory.getLogger(GeolocationServiceImpl.class);

    /**
     * 地理位置 API 集成器。
     */
    private final GeoApiIntegrator geoApiIntegrator;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数。
     *
     * @param geoApiIntegrator 地理位置 API 集成器
     */
    GeolocationServiceImpl(GeoApiIntegrator geoApiIntegrator) {
        this.geoApiIntegrator = geoApiIntegrator;
    }

    /*-------------------- public method --------------------*/

    /**
     * 根据 IP 地址查询详细地址信息。
     *
     * @param param IP 地址查询参数
     * @return 详细地址信息
     */
    @Override
    public Mono<AddressDetail> getAddressByIp(IpAddressQueryParam param) {

        logger.debug("query address by ip: {}", param.getIp());
        return geoApiIntegrator.getAddressDetailByIp(param.getIp())
                   .switchIfEmpty(Mono.<AddressDetail>empty().doOnSubscribe(subscription -> {
                       logger.warn("failed to get address detail for ip: {}", param.getIp());
                   }));
    }

}