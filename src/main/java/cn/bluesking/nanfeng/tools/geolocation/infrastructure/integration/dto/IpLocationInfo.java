package cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto;

/**
 * IP 位置信息数据传输对象，用于表示从 IP 查询获取的位置信息。
 *
 * <AUTHOR>
 * @date 2025-05-15
 * @since 2.1.6
 */
public class IpLocationInfo {

    /**
     * IP 地址。
     */
    private String ip;

    /**
     * 纬度。
     */
    private Double latitude;

    /**
     * 经度。
     */
    private Double longitude;

    /**
     * 国家。
     */
    private String country;

    /**
     * 省份。
     */
    private String province;

    /**
     * 城市。
     */
    private String city;

    /**
     * 区县。
     */
    private String district;

    /*-------------------- getter --------------------*/

    public String getIp() {
        return ip;
    }

    public Double getLatitude() {
        return latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public String getCountry() {
        return country;
    }

    public String getProvince() {
        return province;
    }

    public String getCity() {
        return city;
    }

    public String getDistrict() {
        return district;
    }

    /*-------------------- setter --------------------*/

    public void setIp(String ip) {
        this.ip = ip;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    /*-------------------- setter --------------------*/

    @Override
    public String toString() {
        return "{\"IpLocationInfo\":{" +
                   "\"ip\": \"" + ip + '\"' +
                   ", \"latitude\": " + latitude +
                   ", \"longitude\": " + longitude +
                   ", \"country\": \"" + country + '\"' +
                   ", \"province\": \"" + province + '\"' +
                   ", \"city\": \"" + city + '\"' +
                   ", \"district\": \"" + district + '\"' +
                   "}}";
    }

}