package cn.bluesking.nanfeng.tools.geolocation.service;

import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto.AddressDetail;
import cn.bluesking.nanfeng.tools.geolocation.service.dto.IpAddressQueryParam;

import reactor.core.publisher.Mono;

/**
 * 地理位置服务接口，提供根据 IP 地址查询详细地址信息的能力。
 * <p>统一调用各平台地理位置 API，根据 IP 地址获取详细地址信息。
 *
 * <AUTHOR>
 * @date 2025-05-06
 * @since 2.1.0
 */
public interface GeolocationService {

    /**
     * 根据 IP 地址查询详细地址信息。
     *
     * @param param IP 地址查询参数
     * @return 详细地址信息
     */
    Mono<AddressDetail> getAddressByIp(IpAddressQueryParam param);
}