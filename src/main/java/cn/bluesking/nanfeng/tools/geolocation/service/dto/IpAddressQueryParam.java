package cn.bluesking.nanfeng.tools.geolocation.service.dto;

/**
 * IP 地址查询参数。
 *
 * <AUTHOR>
 * @date 2025-05-15
 * @since 2.1.6
 */
public class IpAddressQueryParam {

    /**
     * IP 地址。
     */
    private String ip;

    /*-------------------- constructor --------------------*/

    /**
     * 默认构造函数。
     */
    public IpAddressQueryParam() {
    }

    /**
     * 构造函数。
     *
     * @param ip IP 地址
     */
    public IpAddressQueryParam(String ip) {
        this.ip = ip;
    }

    /*-------------------- getter --------------------*/

    /**
     * 获取 IP 地址。
     *
     * @return IP 地址
     */
    public String getIp() {
        return ip;
    }

    /*-------------------- setter --------------------*/

    /**
     * 设置 IP 地址。
     *
     * @param ip IP 地址
     */
    public void setIp(String ip) {
        this.ip = ip;
    }

    /*-------------------- setter --------------------*/

    @Override
    public String toString() {
        return "{\"IpAddressQueryParam\":{" +
                   "\"ip\": \"" + ip + '\"' +
                   "}}";
    }

}