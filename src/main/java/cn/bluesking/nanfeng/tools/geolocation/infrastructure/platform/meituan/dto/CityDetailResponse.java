package cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.meituan.dto;

/**
 * 城市详情响应模型。
 *
 * <AUTHOR>
 */
public class CityDetailResponse {

    /**
     * 响应数据
     */
    private CityData data;

    /*-------------------- getter --------------------*/

    public CityData getData() {
        return data;
    }

    /*-------------------- setter --------------------*/

    public void setData(CityData data) {
        this.data = data;
    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "{\"CityDetailResponse\":{" +
                   "\"data\": " + data +
                   "}}";
    }

    /*-------------------- inner class --------------------*/

    /**
     * 城市详情数据
     */
    public static class CityData {
        /**
         * 详细地址
         */
        private String detail;

        /**
         * 父区域 ID
         */
        private Integer parentArea;

        /**
         * 城市拼音
         */
        private String cityPinyin;

        /**
         * 经度
         */
        private Double lng;

        /**
         * 是否国外
         */
        private Boolean isForeign;

        /**
         * 大众点评城市 ID
         */
        private Integer dpCityId;

        /**
         * 国家
         */
        private String country;

        /**
         * 是否开放
         */
        private Boolean isOpen;

        /**
         * 城市名称
         */
        private String city;

        /**
         * ID
         */
        private Integer id;

        /**
         * 开放城市名称
         */
        private String openCityName;

        /**
         * 原始城市 ID
         */
        private Integer originCityID;

        /**
         * 区域 ID
         */
        private Integer area;

        /**
         * 区域名称
         */
        private String areaName;

        /**
         * 省份
         */
        private String province;

        /**
         * 区县
         */
        private String district;

        /**
         * 纬度
         */
        private Double lat;

        /*-------------------- getter --------------------*/

        public String getDetail() {
            return detail;
        }

        public Integer getParentArea() {
            return parentArea;
        }

        public String getCityPinyin() {
            return cityPinyin;
        }

        public Double getLng() {
            return lng;
        }

        public Boolean getForeign() {
            return isForeign;
        }

        public Integer getDpCityId() {
            return dpCityId;
        }

        public String getCountry() {
            return country;
        }

        public Boolean getOpen() {
            return isOpen;
        }

        public String getCity() {
            return city;
        }

        public Integer getId() {
            return id;
        }

        public String getOpenCityName() {
            return openCityName;
        }

        public Integer getOriginCityID() {
            return originCityID;
        }

        public Integer getArea() {
            return area;
        }

        public String getAreaName() {
            return areaName;
        }

        public String getProvince() {
            return province;
        }

        public String getDistrict() {
            return district;
        }

        public Double getLat() {
            return lat;
        }

        /*-------------------- setter --------------------*/

        public void setDetail(String detail) {
            this.detail = detail;
        }

        public void setParentArea(Integer parentArea) {
            this.parentArea = parentArea;
        }

        public void setCityPinyin(String cityPinyin) {
            this.cityPinyin = cityPinyin;
        }

        public void setLng(Double lng) {
            this.lng = lng;
        }

        public void setForeign(Boolean foreign) {
            isForeign = foreign;
        }

        public void setDpCityId(Integer dpCityId) {
            this.dpCityId = dpCityId;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public void setOpen(Boolean open) {
            isOpen = open;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public void setOpenCityName(String openCityName) {
            this.openCityName = openCityName;
        }

        public void setOriginCityID(Integer originCityID) {
            this.originCityID = originCityID;
        }

        public void setArea(Integer area) {
            this.area = area;
        }

        public void setAreaName(String areaName) {
            this.areaName = areaName;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public void setDistrict(String district) {
            this.district = district;
        }

        public void setLat(Double lat) {
            this.lat = lat;
        }

        /*-------------------- toString --------------------*/

        @Override
        public String toString() {
            return "{\"CityData\":{" +
                       "\"detail\": \"" + detail + '\"' +
                       ", \"parentArea\": " + parentArea +
                       ", \"cityPinyin\": \"" + cityPinyin + '\"' +
                       ", \"lng\": " + lng +
                       ", \"isForeign\": " + isForeign +
                       ", \"dpCityId\": " + dpCityId +
                       ", \"country\": \"" + country + '\"' +
                       ", \"isOpen\": " + isOpen +
                       ", \"city\": \"" + city + '\"' +
                       ", \"id\": " + id +
                       ", \"openCityName\": \"" + openCityName + '\"' +
                       ", \"originCityID\": " + originCityID +
                       ", \"area\": " + area +
                       ", \"areaName\": \"" + areaName + '\"' +
                       ", \"province\": \"" + province + '\"' +
                       ", \"district\": \"" + district + '\"' +
                       ", \"lat\": " + lat +
                       "}}";
        }

    }

}