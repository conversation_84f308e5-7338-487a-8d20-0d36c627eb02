package cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto;

import java.util.HashMap;
import java.util.Map;

/**
 * 地址详细信息类。
 *
 * <AUTHOR>
 * @date 2025-05-15
 * @since 2.1.6
 */
public class AddressDetail {

    /**
     * IP 地址。
     */
    private String ip;

    /**
     * 经度。
     */
    private Double longitude;

    /**
     * 纬度。
     */
    private Double latitude;

    /**
     * 国家。
     */
    private String country;

    /**
     * 省份。
     */
    private String province;

    /**
     * 城市。
     */
    private String city;

    /**
     * 区县。
     */
    private String district;

    /**
     * 详细地址。
     */
    private String detail;

    /**
     * 区域名称。
     */
    private String areaName;

    /**
     * 数据源信息，存储来自不同 API 的解析结果。
     */
    private Map<String, SourceAddressDetail> sourceDetails = new HashMap<>();

    /*-------------------- getter --------------------*/

    /**
     * 获取 IP 地址。
     *
     * @return IP 地址
     */
    public String getIp() {
        return ip;
    }

    /**
     * 获取经度。
     *
     * @return 经度
     */
    public Double getLongitude() {
        return longitude;
    }

    /**
     * 获取纬度。
     *
     * @return 纬度
     */
    public Double getLatitude() {
        return latitude;
    }

    /**
     * 获取国家。
     *
     * @return 国家
     */
    public String getCountry() {
        return country;
    }

    /**
     * 获取省份。
     *
     * @return 省份
     */
    public String getProvince() {
        return province;
    }

    /**
     * 获取城市。
     *
     * @return 城市
     */
    public String getCity() {
        return city;
    }

    /**
     * 获取区县。
     *
     * @return 区县
     */
    public String getDistrict() {
        return district;
    }

    /**
     * 获取详细地址。
     *
     * @return 详细地址
     */
    public String getDetail() {
        return detail;
    }

    /**
     * 获取区域名称。
     *
     * @return 区域名称
     */
    public String getAreaName() {
        return areaName;
    }

    /**
     * 获取数据源信息。
     *
     * @return 数据源信息
     */
    public Map<String, SourceAddressDetail> getSourceDetails() {
        return sourceDetails;
    }

    /*-------------------- setter --------------------*/

    /**
     * 设置 IP 地址。
     *
     * @param ip IP 地址
     */
    public void setIp(String ip) {
        this.ip = ip;
    }

    /**
     * 设置经度。
     *
     * @param longitude 经度
     */
    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    /**
     * 设置纬度。
     *
     * @param latitude 纬度
     */
    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    /**
     * 设置国家。
     *
     * @param country 国家
     */
    public void setCountry(String country) {
        this.country = country;
    }

    /**
     * 设置省份。
     *
     * @param province 省份
     */
    public void setProvince(String province) {
        this.province = province;
    }

    /**
     * 设置城市。
     *
     * @param city 城市
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * 设置区县。
     *
     * @param district 区县
     */
    public void setDistrict(String district) {
        this.district = district;
    }

    /**
     * 设置详细地址。
     *
     * @param detail 详细地址
     */
    public void setDetail(String detail) {
        this.detail = detail;
    }

    /**
     * 设置区域名称。
     *
     * @param areaName 区域名称
     */
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    /**
     * 设置数据源信息。
     *
     * @param sourceDetails 数据源信息
     */
    public void setSourceDetails(Map<String, SourceAddressDetail> sourceDetails) {
        this.sourceDetails = sourceDetails;
    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "{\"AddressDetail\":{" +
                   "\"ip\": \"" + ip + '\"' +
                   ", \"longitude\": " + longitude +
                   ", \"latitude\": " + latitude +
                   ", \"country\": \"" + country + '\"' +
                   ", \"province\": \"" + province + '\"' +
                   ", \"city\": \"" + city + '\"' +
                   ", \"district\": \"" + district + '\"' +
                   ", \"detail\": \"" + detail + '\"' +
                   ", \"areaName\": \"" + areaName + '\"' +
                   ", \"sourceDetails\": " + sourceDetails +
                   "}}";
    }

    /*-------------------- public method --------------------*/

    /**
     * 添加数据源详情。
     *
     * @param source 数据源名称
     * @param detail 数据源详情
     */
    public void addSourceDetail(String source, SourceAddressDetail detail) {
        this.sourceDetails.put(source, detail);
    }

    /*-------------------- inner class --------------------*/

    /**
     * 数据源地址详情类。
     */
    public static class SourceAddressDetail {
        /**
         * 国家。
         */
        private String country;

        /**
         * 省份。
         */
        private String province;

        /**
         * 城市。
         */
        private String city;

        /**
         * 区县。
         */
        private String district;

        /**
         * 街道。
         */
        private String street;

        /**
         * 详细地址。
         */
        private String formattedAddress;

        /**
         * 额外信息。
         */
        private Map<String, String> extraInfo = new HashMap<>();

        /*-------------------- getter --------------------*/

        /**
         * 获取国家。
         *
         * @return 国家
         */
        public String getCountry() {
            return country;
        }

        /**
         * 获取省份。
         *
         * @return 省份
         */
        public String getProvince() {
            return province;
        }

        /**
         * 获取城市。
         *
         * @return 城市
         */
        public String getCity() {
            return city;
        }

        /**
         * 获取区县。
         *
         * @return 区县
         */
        public String getDistrict() {
            return district;
        }

        /**
         * 获取街道。
         *
         * @return 街道
         */
        public String getStreet() {
            return street;
        }

        /**
         * 获取格式化地址。
         *
         * @return 格式化地址
         */
        public String getFormattedAddress() {
            return formattedAddress;
        }

        /**
         * 获取额外信息。
         *
         * @return 额外信息
         */
        public Map<String, String> getExtraInfo() {
            return extraInfo;
        }

        /*-------------------- setter --------------------*/

        /**
         * 设置国家。
         *
         * @param country 国家
         */
        public void setCountry(String country) {
            this.country = country;
        }

        /**
         * 设置省份。
         *
         * @param province 省份
         */
        public void setProvince(String province) {
            this.province = province;
        }

        /**
         * 设置城市。
         *
         * @param city 城市
         */
        public void setCity(String city) {
            this.city = city;
        }

        /**
         * 设置区县。
         *
         * @param district 区县
         */
        public void setDistrict(String district) {
            this.district = district;
        }

        /**
         * 设置街道。
         *
         * @param street 街道
         */
        public void setStreet(String street) {
            this.street = street;
        }

        /**
         * 设置格式化地址。
         *
         * @param formattedAddress 格式化地址
         */
        public void setFormattedAddress(String formattedAddress) {
            this.formattedAddress = formattedAddress;
        }

        /**
         * 设置额外信息。
         *
         * @param extraInfo 额外信息
         */
        public void setExtraInfo(Map<String, String> extraInfo) {
            this.extraInfo = extraInfo;
        }

        /**
         * 添加额外信息。
         *
         * @param key 键
         * @param value 值
         */
        public void addExtraInfo(String key, String value) {
            this.extraInfo.put(key, value);
        }

        /*-------------------- toString --------------------*/

        @Override
        public String toString() {
            return "{\"SourceAddressDetail\":{" +
                       "\"country\": \"" + country + '\"' +
                       ", \"province\": \"" + province + '\"' +
                       ", \"city\": \"" + city + '\"' +
                       ", \"district\": \"" + district + '\"' +
                       ", \"street\": \"" + street + '\"' +
                       ", \"formattedAddress\": \"" + formattedAddress + '\"' +
                       ", \"extraInfo\": " + extraInfo +
                       "}}";
        }

    }

}