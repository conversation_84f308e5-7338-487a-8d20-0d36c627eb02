package cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.meituan.impl;

import org.springframework.stereotype.Service;

import cn.bluesking.nanfeng.tools.common.http.ApiClient;
import cn.bluesking.nanfeng.tools.common.http.ApiClientFactory;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.meituan.MeituanApi;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.meituan.dto.CityDetailResponse;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.meituan.dto.IpLocationResponse;

import reactor.core.publisher.Mono;

/**
 * 美团 API 接口实现类。
 *
 * <AUTHOR>
 */
@Service
class MeituanApiImpl implements MeituanApi {

    /**
     * API 基础 URL。
     */
    private static final String BASE_URL = "https://apimobile.meituan.com";

    /**
     * API 客户端。
     */
    private final ApiClient apiClient;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数。
     */
    public MeituanApiImpl() {
        this.apiClient = ApiClientFactory.builder()
                             .baseUrl(BASE_URL)
                             .build();
    }

    /*-------------------- public method --------------------*/

    @Override
    public Mono<IpLocationResponse> getLocationByIp(String ip) {
        return apiClient.request()
                   .uri("/locate/v2/ip/loc?rgeo=true&ip=" + ip)
                   .get()
                   .retrieveJson(IpLocationResponse.class);
    }

    @Override
    public Mono<CityDetailResponse> getCityDetailByLocation(Double lat, Double lng) {
        return apiClient.request()
                   .uri("/group/v1/city/latlng/" + lat + "," + lng + "?tag=0")
                   .get()
                   .retrieveJson(CityDetailResponse.class);
    }

}