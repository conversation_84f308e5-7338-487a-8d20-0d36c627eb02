package cn.bluesking.nanfeng.tools.geolocation.rest;

import jakarta.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.bluesking.nanfeng.tools.common.utils.IpUtils;
import cn.bluesking.nanfeng.tools.common.web.Response;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto.AddressDetail;
import cn.bluesking.nanfeng.tools.geolocation.service.GeolocationService;
import cn.bluesking.nanfeng.tools.geolocation.service.dto.IpAddressQueryParam;

import reactor.core.publisher.Mono;

/**
 * 地理位置 API 控制器，提供 IP 地址查询相关接口。
 *
 * <AUTHOR>
 * @date 2025-05-15
 * @since 2.1.6
 */
@RestController
@RequestMapping("/geolocation")
public class GeolocationController {

    /**
     * 日志记录器。
     */
    private static final Logger logger = LoggerFactory.getLogger(GeolocationController.class);

    /**
     * 地理位置服务接口。
     */
    private final GeolocationService geolocationService;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数。
     *
     * @param geolocationService 地理位置服务接口
     */
    public GeolocationController(GeolocationService geolocationService) {
        this.geolocationService = geolocationService;
    }

    /*-------------------- public method --------------------*/

    /**
     * 根据当前请求的客户端 IP 查询详细地址信息。
     *
     * @param request HTTP 请求对象
     * @return 包含详细地址信息的响应
     */
    @PostMapping("/ip-address")
    public Mono<Response<AddressDetail>> getAddressByClientIp(HttpServletRequest request) {

        // 获取客户端 IP
        String clientIp = IpUtils.getClientIp(request);
        logger.debug("client ip: {}", clientIp);

        // 构造服务查询参数
        IpAddressQueryParam param = new IpAddressQueryParam(clientIp);

        // 调用服务查询地址信息
        return geolocationService.getAddressByIp(param)
                .map(Response::success)
                .switchIfEmpty(Mono.just(Response.error("未能获取地址信息")));
    }

}