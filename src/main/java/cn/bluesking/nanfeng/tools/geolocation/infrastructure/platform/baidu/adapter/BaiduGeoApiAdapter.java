package cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.baidu.adapter;

import cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.baidu.dto.BaiduMapResponse.AddressComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.GeoApiAdapter;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.Platform;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.integration.dto.LocationInfo;
import cn.bluesking.nanfeng.tools.geolocation.infrastructure.platform.baidu.BaiduMapApi;

import reactor.core.publisher.Mono;

/**
 * 百度地图 API 适配器实现类。
 *
 * <AUTHOR>
 */
@Component
class BaiduGeoApiAdapter implements GeoApiAdapter {

    /**
     * 日志记录器。
     */
    private static final Logger logger = LoggerFactory.getLogger(BaiduGeoApiAdapter.class);

    /**
     * 百度地图 API 接口。
     */
    private final BaiduMapApi baiduMapApi;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数。
     *
     * @param baiduMapApi 百度地图 API 接口
     */
    BaiduGeoApiAdapter(BaiduMapApi baiduMapApi) {
        this.baiduMapApi = baiduMapApi;
    }

    /*-------------------- public method --------------------*/

    @Override
    public Platform getPlatform() {
        return Platform.BAIDU;
    }

    @Override
    public Mono<LocationInfo> getAddressByLocation(Double latitude, Double longitude) {
        return baiduMapApi.getAddressByLocation(latitude, longitude)
                   .map(response -> {
                       LocationInfo locationInfo = new LocationInfo();

                       if (response.getStatus() == 0 && response.getResult() != null) {
                           locationInfo.setFormattedAddress(response.getResult().getFormattedAddress());

                           if (response.getResult().getAddressComponent() != null) {

                               AddressComponent component = response.getResult().getAddressComponent();

                               locationInfo.setCountry(component.getCountry());
                               locationInfo.setProvince(component.getProvince());
                               locationInfo.setCity(component.getCity());
                               locationInfo.setDistrict(component.getDistrict());
                               locationInfo.setStreet(component.getStreet());

                               locationInfo.addExtraInfo("streetNumber", component.getStreetNumber());
                               locationInfo.addExtraInfo("town", component.getTown());
                               locationInfo.addExtraInfo("adcode", component.getAdcode());
                           }

                           locationInfo.addExtraInfo("business", response.getResult().getBusiness());
                       }

                       return locationInfo;
                   })
                   .onErrorResume(e -> {
                       logger.warn("failed to get address from Baidu API.", e);
                       return Mono.empty();
                   });
    }

}