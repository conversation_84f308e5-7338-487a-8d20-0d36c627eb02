package cn.bluesking.nanfeng.tools.outlook.impl;

import static cn.bluesking.nanfeng.tools.common.utils.ValidationUtils.*;
import static cn.bluesking.nanfeng.tools.outlook.impl.EmailMessagePlaceholderValuesHelper.fillPlaceholder;

import java.io.*;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.aspose.email.*;

import cn.bluesking.nanfeng.tools.outlook.EmailInfo;
import cn.bluesking.nanfeng.tools.outlook.EmailInfoBuilderProperties;

/**
 * 基于 Aspose Email 的电子邮件信息服务实现
 *
 * <AUTHOR>
 */
@Service
class EmailInfoServiceV2Impl extends AbstractEmailInfoService {

    @Override
    public void writeToMsgFile(boolean isExportAsEml,
                               EmailInfo emailInfo,
                               File file,
                               EmailInfoBuilderProperties emailInfoBuilderProperties) throws IOException {

        assertNotNull(emailInfo);
        assertNotNull(file);

        if (!file.exists()) {
            FileUtils.touch(file);
        }

        assertTrue(file.isFile());

        EmailMessagePlaceholderValues placeholderValues = EmailMessagePlaceholderValuesHelper.build(emailInfo,
                emailInfoBuilderProperties);

        String hi = placeholderValues.getHi();
        String subject = placeholderValues.getSubject();
        String content = placeholderValues.getContent();

        File msgTemplateFile = emailInfoBuilderProperties.getMsgTemplateFile();
        try (InputStream is = msgTemplateFile == null ? null : new FileInputStream(msgTemplateFile);
             MailMessage message = is == null ? new MailMessage() : MailMessage.load(is)) {

            List<EmailInfo.RecipientInfo> recipientInfos = emailInfo.getRecipientInfos();
            assertNotEmpty(recipientInfos);
            for (EmailInfo.RecipientInfo recipientInfo : recipientInfos) {

                String name = recipientInfo.getName();
                String emailAddress = recipientInfo.getEmailAddress();
                message.getTo().addItem(new MailAddress(emailAddress, name, false));
            }

            if (StringUtils.isNotEmpty(subject)) {
                message.setSubject(subject);
            }

            String htmlBody = message.getHtmlBody();
            if (StringUtils.isEmpty(htmlBody)) {
                message.setBody(
                        StringUtils.defaultIfEmpty(hi, StringUtils.EMPTY) +
                        "\n" +
                        StringUtils.defaultIfEmpty(content, StringUtils.EMPTY));
            }
            else {

                // 删除图片后的空行标签
                htmlBody = htmlBody.replaceAll("(<img [\\s\\S]+?)<o:p></o:p>", "$1");
                // 删除双括号中间的 SpellE 等标签
                htmlBody = htmlBody.replaceAll("(\\{\\{\\s*)<.+?>(.*?)<\\/.+?>(\\s*\\}\\})", "$1$2$3");
                message.setHtmlBody(fillPlaceholder(htmlBody, placeholderValues));
            }

            try (MapiMessage mapiMessage = MapiMessage.fromMailMessage(message);
                 FileOutputStream fos = new FileOutputStream(file)) {

                mapiMessage.setMessageFlags(MapiMessageFlags.MSGFLAG_UNSENT | MapiMessageFlags.MSGFLAG_FROMME);
                SaveOptions saveOptions = isExportAsEml
                        ? SaveOptions.getDefaultEml()
                        : SaveOptions.getDefaultMsgUnicode();
                mapiMessage.save(fos, saveOptions);
            }
        }
    }

}