package cn.bluesking.nanfeng.tools.outlook.impl;

import java.util.Objects;

/**
 * 邮件信息支持的占位符。
 *
 * <AUTHOR>
 */
class EmailMessagePlaceholderValues {

    private String companyName;

    private String content;

    private String hey;

    private String hi;

    private String recipientFirstNames;

    private String subject;

    /*-------------------- getter --------------------*/

    /**
     * 获取公司名称。
     *
     * @return 公司名称
     */
    public String getCompanyName() {
        return companyName;
    }

    /**
     * 获取邮件内容。
     *
     * @return 邮件内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 获取 hey 打招呼语。
     *
     * @return hey 打招呼语
     */
    public String getHey() {
        return hey;
    }

    /**
     * 获取 hi 打招呼语。
     *
     * @return hi 打招呼语
     */
    public String getHi() {
        return hi;
    }

    /**
     * 获取收件人名字列表。
     *
     * @return 收件人名字列表
     */
    public String getRecipientFirstNames() {
        return recipientFirstNames;
    }

    /**
     * 获取邮件主题。
     *
     * @return 邮件主题
     */
    public String getSubject() {
        return subject;
    }

    /*-------------------- setter --------------------*/

    /**
     * 设置公司名称。
     *
     * @param companyName 公司名称
     */
    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    /**
     * 设置邮件内容。
     *
     * @param content 邮件内容
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 设置 hey 打招呼语。
     *
     * @param hey hey 打招呼语
     */
    public void setHey(String hey) {
        this.hey = hey;
    }

    /**
     * 设置 hi 打招呼语。
     *
     * @param hi hi 打招呼语
     */
    public void setHi(String hi) {
        this.hi = hi;
    }

    /**
     * 设置收件人名字列表。
     *
     * @param recipientFirstNames 收件人名字列表
     */
    public void setRecipientFirstNames(String recipientFirstNames) {
        this.recipientFirstNames = recipientFirstNames;
    }

    /**
     * 设置邮件主题。
     *
     * @param subject 邮件主题
     */
    public void setSubject(String subject) {
        this.subject = subject;
    }

    /*-------------------- equals and hashCode --------------------*/

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        EmailMessagePlaceholderValues that = (EmailMessagePlaceholderValues) o;
        return Objects.equals(companyName, that.companyName) &&
                   Objects.equals(content, that.content) &&
                   Objects.equals(hey, that.hey) &&
                   Objects.equals(hi, that.hi) &&
                   Objects.equals(recipientFirstNames, that.recipientFirstNames) &&
                   Objects.equals(subject, that.subject);
    }

    @Override
    public int hashCode() {
        return Objects.hash(companyName, content, hey, hi, recipientFirstNames, subject);
    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "EmailMessagePlaceholderValues{" +
                   "companyName='" + companyName + '\'' +
                   ", content='" + content + '\'' +
                   ", hey='" + hey + '\'' +
                   ", hi='" + hi + '\'' +
                   ", recipientFirstNames='" + recipientFirstNames + '\'' +
                   ", subject='" + subject + '\'' +
                   '}';
    }

}