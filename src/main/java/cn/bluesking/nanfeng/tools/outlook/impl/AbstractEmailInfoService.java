package cn.bluesking.nanfeng.tools.outlook.impl;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import cn.bluesking.nanfeng.tools.outlook.EmailInfo;
import cn.bluesking.nanfeng.tools.outlook.EmailInfoBuilderProperties;
import cn.bluesking.nanfeng.tools.outlook.EmailInfoService;
import cn.bluesking.nanfeng.tools.outlook.SheetEmailInfo;

/**
 * 抽象的邮箱信息服务实现类。
 *
 * <AUTHOR>
 */
abstract class AbstractEmailInfoService implements EmailInfoService {

    private final SheetEmailInfosReader sheetEmailInfosReader;

    /*-------------------- constructor --------------------*/

    protected AbstractEmailInfoService() {
        this(new SheetEmailInfosReader());
    }

    protected AbstractEmailInfoService(SheetEmailInfosReader sheetEmailInfosReader) {
        this.sheetEmailInfosReader = sheetEmailInfosReader;
    }

    /*-------------------- public method --------------------*/

    @Override
    public List<SheetEmailInfo> readFromExcel(File file) throws IOException {
        return sheetEmailInfosReader.readFromExcel(file);
    }

    @Override
    public EmailInfoBuilderProperties readRefineEmailInfoProperties(String subjectTemplate,
                                                                    String contentTemplate,
                                                                    File msgTemplateFile) {

        EmailInfoBuilderProperties emailInfoBuilderProperties = new EmailInfoBuilderProperties();

        emailInfoBuilderProperties.setContentTemplate(contentTemplate);
        emailInfoBuilderProperties.setSubjectTemplate(subjectTemplate);
        emailInfoBuilderProperties.setMsgTemplateFile(msgTemplateFile);

        return emailInfoBuilderProperties;
    }

    @Override
    public List<EmailInfo> splitForOneEmailInfoPerRecipient(List<EmailInfo> emailInfos) {

        List<EmailInfo> oneRecipientEmailInfos = new ArrayList<>();
        for (EmailInfo emailInfo : emailInfos) {

            List<EmailInfo.RecipientInfo> recipientInfos = emailInfo.getRecipientInfos();
            if (recipientInfos.size() == 1) {
                oneRecipientEmailInfos.add(emailInfo);
            }
            else {

                for (EmailInfo.RecipientInfo recipientInfo : recipientInfos) {

                    EmailInfo oneRecipientEmailInfo = new EmailInfo();

                    oneRecipientEmailInfo.setCompanyName(emailInfo.getCompanyName());
                    oneRecipientEmailInfo.setRecipientInfos(Collections.singletonList(recipientInfo));

                    oneRecipientEmailInfos.add(oneRecipientEmailInfo);
                }
            }
        }

        return oneRecipientEmailInfos;
    }

    /*-------------------- private method --------------------*/

}