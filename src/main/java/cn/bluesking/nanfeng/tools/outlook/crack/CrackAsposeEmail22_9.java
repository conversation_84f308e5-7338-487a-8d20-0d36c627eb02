package cn.bluesking.nanfeng.tools.outlook.crack;

import java.io.IOException;
import javassist.*;

/**
 * 破解 Aspose Email For Java 22.9 版本。
 *
 * <AUTHOR>
 */
public class CrackAsposeEmail22_9 {

    public static void main(String[] args) throws NotFoundException, CannotCompileException, IOException {

        String libsPath = System.getProperty("user.dir") + "/libs/";
        String jarPath = libsPath + "aspose-email-22.9-jdk16.jar.bak";
        ClassPool pool = ClassPool.getDefault();
        pool.insertClassPath(jarPath);

        CtClass ctClass = pool.get("com.aspose.email.zaht");
        CtMethod ctMethod = ctClass.getDeclaredMethod("d");
        ctMethod.setBody("return true;");

        ctClass.writeFile(libsPath);
    }

}