package cn.bluesking.nanfeng.tools.outlook;

import java.io.File;
import java.util.Objects;

/**
 * 改进邮件内容配置属性。
 *
 * <AUTHOR>
 */
public class EmailInfoBuilderProperties {

    /**
     * 邮件内容模板
     */
    private String contentTemplate;

    /**
     * 邮件主题模板
     */
    private String subjectTemplate;

    /**
     * 邮件模板文件
     */
    private File msgTemplateFile;

    /*-------------------- getter --------------------*/

    /**
     * 获取邮件内容模板。
     *
     * @return 邮件内容模板
     */
    public String getContentTemplate() {
        return contentTemplate;
    }

    /**
     * 获取邮件主题模板。
     *
     * @return 邮件主题模板
     */
    public String getSubjectTemplate() {
        return subjectTemplate;
    }

    /**
     * 获取邮件模板文件。
     *
     * @return 邮件模板文件
     */
    public File getMsgTemplateFile() {
        return msgTemplateFile;
    }

    /*-------------------- setter --------------------*/

    /**
     * 设置邮件内容模板。
     *
     * @param contentTemplate 邮件内容模板
     */
    public void setContentTemplate(String contentTemplate) {
        this.contentTemplate = contentTemplate;
    }

    /**
     * 设置邮件主题模板。
     *
     * @param subjectTemplate 邮件主题模板
     */
    public void setSubjectTemplate(String subjectTemplate) {
        this.subjectTemplate = subjectTemplate;
    }

    /**
     * 设置邮件模板文件。
     *
     * @param msgTemplateFile 邮件模板文件
     */
    public void setMsgTemplateFile(File msgTemplateFile) {
        this.msgTemplateFile = msgTemplateFile;
    }

    /*-------------------- Object methods --------------------*/

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        EmailInfoBuilderProperties that = (EmailInfoBuilderProperties) o;
        return Objects.equals(contentTemplate, that.contentTemplate) &&
                   Objects.equals(subjectTemplate, that.subjectTemplate) &&
                   Objects.equals(msgTemplateFile, that.msgTemplateFile);
    }

    @Override
    public int hashCode() {
        return Objects.hash(contentTemplate, subjectTemplate, msgTemplateFile);
    }

    @Override
    public String toString() {
        return "EmailInfoBuilderProperties{" +
                   "contentTemplate='" + contentTemplate + '\'' +
                   ", subjectTemplate='" + subjectTemplate + '\'' +
                   ", msgTemplateFile=" + msgTemplateFile +
                   '}';
    }

}