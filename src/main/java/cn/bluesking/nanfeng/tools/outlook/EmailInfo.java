package cn.bluesking.nanfeng.tools.outlook;

import java.util.List;
import java.util.Objects;

/**
 * 电子邮件信息。
 * <p>用于存储邮件发送的基本信息，包括公司名称、主题、内容和收件人信息。
 *
 * <AUTHOR>
 * @date 2024-02-29
 * @since 1.0.0
 */
public class EmailInfo {

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 邮件主题
     */
    private String subject;

    /**
     * 邮件内容前打招呼语句
     */
    private String hi;

    /**
     * 邮件内容
     */
    private String content;

    /**
     * 收件人信息列表
     */
    private List<RecipientInfo> recipientInfos;

    /*-------------------- getter --------------------*/

    public String getCompanyName() {
        return companyName;
    }

    public String getSubject() {
        return subject;
    }

    public String getHi() {
        return hi;
    }

    public String getContent() {
        return content;
    }

    public List<RecipientInfo> getRecipientInfos() {
        return recipientInfos;
    }

    /*-------------------- setter --------------------*/

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public void setHi(String hi) {
        this.hi = hi;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setRecipientInfos(List<RecipientInfo> recipientInfos) {
        this.recipientInfos = recipientInfos;
    }

    /*-------------------- equals and hashCode --------------------*/

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof EmailInfo emailInfo)) {
            return false;
        }
        return Objects.equals(companyName, emailInfo.companyName) && Objects.equals(subject,
            emailInfo.subject) && Objects.equals(hi, emailInfo.hi) && Objects.equals(content,
            emailInfo.content) && Objects.equals(recipientInfos, emailInfo.recipientInfos);
    }

    @Override
    public int hashCode() {
        return Objects.hash(companyName, subject, hi, content, recipientInfos);
    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "{\"EmailInfo\":{" +
                   "\"companyName\": \"" + companyName + '\"' +
                   ", \"subject\": \"" + subject + '\"' +
                   ", \"hi\": \"" + hi + '\"' +
                   ", \"content\": \"" + content + '\"' +
                   ", \"recipientInfos\": " + recipientInfos +
                   "}}";
    }

    /*-------------------- inner class --------------------*/

    /**
     * 收件人信息。
     * <p>封装收件人的名称和邮件地址信息。
     *
     * <AUTHOR>
     * @date 2024-02-29
     * @since 1.0.0
     */
    public static class RecipientInfo {

        /**
         * 收件人名称
         */
        private String name;

        /**
         * 收件人邮件地址
         */
        private String emailAddress;

        /*-------------------- getter --------------------*/

        public String getName() {
            return name;
        }

        public String getEmailAddress() {
            return emailAddress;
        }

        /*-------------------- setter --------------------*/

        public void setName(String name) {
            this.name = name;
        }

        public void setEmailAddress(String emailAddress) {
            this.emailAddress = emailAddress;
        }

        /*-------------------- equals and hashCode --------------------*/

        @Override
        public boolean equals(Object o) {
            if (!(o instanceof RecipientInfo that)) {
                return false;
            }
            return Objects.equals(name, that.name) && Objects.equals(emailAddress, that.emailAddress);
        }

        @Override
        public int hashCode() {
            return Objects.hash(name, emailAddress);
        }

        /*-------------------- toString --------------------*/

        @Override
        public String toString() {
            return "{\"RecipientInfo\":{" +
                       "\"name\": \"" + name + '\"' +
                       ", \"emailAddress\": \"" + emailAddress + '\"' +
                       "}}";
        }

    }

}