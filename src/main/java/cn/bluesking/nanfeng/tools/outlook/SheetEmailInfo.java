package cn.bluesking.nanfeng.tools.outlook;

import java.util.List;
import java.util.Objects;

/**
 * 单个 Sheet 下的邮件信息。
 *
 * <AUTHOR>
 */
public class SheetEmailInfo {

    /** Sheet 名称 */
    private String sheetName;

    /** Sheet 下读取到的邮件信息列表 */
    private List<EmailInfo> emailInfos;

    /*-------------------- getter --------------------*/

    public String getSheetName() {
        return sheetName;
    }

    public List<EmailInfo> getEmailInfos() {
        return emailInfos;
    }

    /*-------------------- setter --------------------*/

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public void setEmailInfos(List<EmailInfo> emailInfos) {
        this.emailInfos = emailInfos;
    }

    /*-------------------- equals and hashCode --------------------*/

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof SheetEmailInfo that)) {
            return false;
        }
        return Objects.equals(sheetName, that.sheetName) && Objects.equals(emailInfos, that.emailInfos);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sheetName, emailInfos);
    }

    /*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "{\"SheetEmailInfo\":{" +
                   "\"sheetName\": \"" + sheetName + '\"' +
                   ", \"emailInfos\": " + emailInfos +
                   "}}";
    }

}