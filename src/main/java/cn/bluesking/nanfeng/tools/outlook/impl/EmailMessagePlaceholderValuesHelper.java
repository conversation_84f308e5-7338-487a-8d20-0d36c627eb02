package cn.bluesking.nanfeng.tools.outlook.impl;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.bluesking.nanfeng.tools.outlook.EmailInfo;
import cn.bluesking.nanfeng.tools.outlook.EmailInfoBuilderProperties;

/**
 * 邮件信息支持的占位符工具方法。
 *
 * <AUTHOR>
 */
final class EmailMessagePlaceholderValuesHelper {

    private static final Logger logger = LoggerFactory.getLogger(EmailMessagePlaceholderValuesHelper.class);

    /*-------------------- constructor --------------------*/

    private EmailMessagePlaceholderValuesHelper() {
        throw new IllegalStateException("Utility class");
    }

    /*-------------------- public static method --------------------*/

    public static boolean hasPlaceholderValues(EmailMessagePlaceholderValues placeholderValues) {

        if (placeholderValues != null) {

            try {

                BeanInfo beanInfo = Introspector.getBeanInfo(EmailMessagePlaceholderValues.class);
                for (PropertyDescriptor propertyDescriptor : beanInfo.getPropertyDescriptors()) {

                    Method readMethod = propertyDescriptor.getReadMethod();
                    if (readMethod.invoke(placeholderValues) != null) {
                        return true;
                    }
                }
            }
            catch (IntrospectionException | IllegalAccessException | InvocationTargetException e) {

                logger.error("failed to judge whether has placeholder values!", e);
                throw new RuntimeException(e);
            }
        }

        return false;
    }

    public static String fillPlaceholder(String template, EmailMessagePlaceholderValues placeholderValues) {

        if (StringUtils.isNotEmpty(template)
                && template.contains("{{")
                && template.contains("}}")
                && placeholderValues != null) {

            try {

                BeanInfo beanInfo = Introspector.getBeanInfo(EmailMessagePlaceholderValues.class);
                for (PropertyDescriptor propertyDescriptor : beanInfo.getPropertyDescriptors()) {

                    String name = propertyDescriptor.getName();
                    if ("class".equals(name)) {
                        continue;
                    }

                    String placeholder = "{{" + name + "}}";
                    if (template.contains(placeholder)) {

                        Method readMethod = propertyDescriptor.getReadMethod();
                        Object value = readMethod.invoke(placeholderValues);
                        // 没有值的占位符会被替换成空字符串
                        String strValue = value == null ? StringUtils.EMPTY : String.valueOf(value);
                        template = template.replace(placeholder, strValue);
                    }
                }
            }
            catch (IntrospectionException | IllegalAccessException | InvocationTargetException e) {

                logger.error("failed to replace placeholder values!", e);
                throw new RuntimeException(e);
            }
        }

        return template;
    }

    public static EmailMessagePlaceholderValues build(EmailInfo emailInfo, EmailInfoBuilderProperties props) {

        EmailMessagePlaceholderValues placeholderValues = new EmailMessagePlaceholderValues();

        String recipientFirstNames = buildRecipientFirstNames(emailInfo);
        placeholderValues.setHey("Hey " + recipientFirstNames);
        placeholderValues.setHi("Hi " + recipientFirstNames);
        placeholderValues.setRecipientFirstNames(recipientFirstNames);

        placeholderValues.setCompanyName(emailInfo.getCompanyName());

        String subject = fillPlaceholder(props.getSubjectTemplate(), placeholderValues);
        placeholderValues.setSubject(subject);

        String content = fillPlaceholder(props.getContentTemplate(), placeholderValues);

        placeholderValues.setSubject(subject);
        placeholderValues.setContent(content);

        return placeholderValues;
    }

    /*-------------------- private static method --------------------*/

    private static String buildRecipientFirstNames(EmailInfo emailInfo) {

        String delimiter = ", ";
        String recipientFirstNames = emailInfo.getRecipientInfos()
                                         .stream()
                                         .map(EmailInfo.RecipientInfo::getName)
                                         // get first name
                                         .map(recipientName -> recipientName.split(" ")[0])
                                         .collect(Collectors.joining(delimiter));

        int index = recipientFirstNames.lastIndexOf(delimiter);
        if (index != -1) {
            recipientFirstNames = recipientFirstNames.substring(0, index)
                                      + " and "
                                      + recipientFirstNames.substring(index + delimiter.length());
        }

        return recipientFirstNames;
    }

}