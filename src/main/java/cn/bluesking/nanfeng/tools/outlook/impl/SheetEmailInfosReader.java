package cn.bluesking.nanfeng.tools.outlook.impl;

import static cn.bluesking.nanfeng.tools.common.utils.ValidationUtils.assertNotNull;
import static cn.bluesking.nanfeng.tools.common.utils.ValidationUtils.assertTrue;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;

import cn.bluesking.nanfeng.tools.common.utils.PoiUtils;
import cn.bluesking.nanfeng.tools.outlook.EmailInfo;
import cn.bluesking.nanfeng.tools.outlook.SheetEmailInfo;

/**
 * Email 信息读取器。
 *
 * <AUTHOR>
 */
class SheetEmailInfosReader {

    /*-------------------- public method --------------------*/

    public List<SheetEmailInfo> readFromExcel(File file) throws IOException {

        assertNotNull(file);
        assertTrue(file.exists());

        List<SheetEmailInfo> sheetEmailInfos = new ArrayList<>();
        try (Workbook workbook = PoiUtils.readWorkbook(file)) {

            int numberOfSheets = workbook.getNumberOfSheets();
            for (int i = 0; i < numberOfSheets; i++) {

                Sheet sheet = workbook.getSheetAt(i);
                List<EmailInfo> emailInfos = readFromSheet(sheet);
                if (CollectionUtils.isNotEmpty(emailInfos)) {

                    SheetEmailInfo sheetEmailInfo = new SheetEmailInfo();
                    sheetEmailInfo.setSheetName(sheet.getSheetName());
                    sheetEmailInfo.setEmailInfos(emailInfos);

                    sheetEmailInfos.add(sheetEmailInfo);
                }

            }

        }

        return sheetEmailInfos;
    }

    /*-------------------- private method --------------------*/

    private static final Set<String> companyColumnNameSet = Set.of("公司", "company", "company name");

    private static final Set<String> concatColumnNameSet = Set.of("联系人", "concat", "concat 联系人", "contact",
        "contact 联系人");

    private List<EmailInfo> readFromSheet(Sheet sheet) {

        Row firstRow = sheet.getRow(sheet.getFirstRowNum());

        if (firstRow == null) {
            return Collections.emptyList();
        }

        Integer companyNameColumnIndex = null;
        Integer recipientFirstColumnIndex = null;
        Integer recipientLastColumnIndex = null;
        for (int i = Math.max(firstRow.getFirstCellNum(), 0); i <= firstRow.getLastCellNum(); i++) {

            Cell cell = firstRow.getCell(i);
            if (cell != null) {

                String title = cell.getStringCellValue();
                if (containsIgnoreCase(title, companyColumnNameSet)) {
                    companyNameColumnIndex = cell.getColumnIndex();
                }

                else if (containsIgnoreCase(title, concatColumnNameSet)) {

                    CellRangeAddress mergeRegion = PoiUtils.getMergeRegion(cell);
                    if (mergeRegion != null) {
                        recipientFirstColumnIndex = mergeRegion.getFirstColumn();
                        recipientLastColumnIndex = mergeRegion.getLastColumn();
                    }

                    else {
                        recipientFirstColumnIndex = cell.getColumnIndex();
                        recipientLastColumnIndex = cell.getColumnIndex();
                    }

                }

            }

        }

        if (recipientFirstColumnIndex == null) {
            return Collections.emptyList();
        }

        else {

            List<EmailInfo> emailInfos = new ArrayList<>();
            for (int i = sheet.getFirstRowNum() + 1; i <= sheet.getLastRowNum(); i++) {

                Row row = sheet.getRow(i);
                if (row != null) {

                    List<EmailInfo.RecipientInfo> recipientInfos = new ArrayList<>();
                    for (int k = recipientFirstColumnIndex; k <= recipientLastColumnIndex; k++) {

                        Cell cell = row.getCell(k);
                        if (cell != null) {

                            EmailInfo.RecipientInfo recipientInfo = readRecipientInfoFromCell(cell);
                            if (recipientInfo != null) {
                                recipientInfos.add(recipientInfo);
                            }

                        }

                    }

                    if (CollectionUtils.isNotEmpty(recipientInfos)) {

                        EmailInfo emailInfo = new EmailInfo();

                        Cell companyNameCell = companyNameColumnIndex == null
                                                   ? null
                                                   : row.getCell(companyNameColumnIndex);
                        if (companyNameCell != null) {
                            // 忽略 companyName 为空的行
                            emailInfo.setCompanyName(PoiUtils.getCellStringValue(companyNameCell));
                        }

                        emailInfo.setRecipientInfos(recipientInfos);
                        if (StringUtils.isNotEmpty(emailInfo.getCompanyName())) {
                            emailInfos.add(emailInfo);
                        }

                    }

                }

            }

            return emailInfos;
        }

    }

    private boolean containsIgnoreCase(String title, Set<String> columnNameSet) {
        return columnNameSet.stream().anyMatch(name -> StringUtils.containsIgnoreCase(title, name));
    }

    private EmailInfo.RecipientInfo readRecipientInfoFromCell(Cell cell) {

        String stringCellValue = PoiUtils.getCellStringValue(cell);
        if (StringUtils.isNotEmpty(stringCellValue)) {

            EmailInfo.RecipientInfo recipientInfo = new EmailInfo.RecipientInfo();
            String[] lines = stringCellValue.split("[\r\n]+");
            if (ArrayUtils.isNotEmpty(lines)) {

                boolean first = true;
                for (String line : lines) {

                    if (StringUtils.isNotEmpty(line)) {

                        if (first) {

                            recipientInfo.setName(line.trim());
                            first = false;
                        }

                        else if (isEmailAddress(line)) {

                            recipientInfo.setEmailAddress(collectEmailAddress(line));
                            return recipientInfo;
                        }

                    }

                }

            }

        }

        return null;
    }

    private String collectEmailAddress(String line) {
        return line.replaceAll("^.*?(\\w+([\\-\\+\\.]\\w+)*@\\w+([\\-\\.]\\w+)*\\.\\w+([\\-\\.]\\w+)*).*?$", "$1");
    }

    private boolean isEmailAddress(String line) {
        return line.matches("^.*?\\w+([\\-\\+\\.]\\w+)*@\\w+([\\-\\.]\\w+)*\\.\\w+([\\-\\.]\\w+)*.*?$");
    }

}