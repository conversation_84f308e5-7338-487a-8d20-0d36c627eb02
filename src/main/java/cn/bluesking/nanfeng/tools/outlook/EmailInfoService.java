package cn.bluesking.nanfeng.tools.outlook;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 电子邮件信息服务类。
 *
 * <AUTHOR>
 */
public interface EmailInfoService {

    List<SheetEmailInfo> readFromExcel(File file) throws IOException;

    EmailInfoBuilderProperties readRefineEmailInfoProperties(String subjectTemplate,
                                                             String contentTemplate,
                                                             File msgTemplateFile) throws IOException;

    List<EmailInfo> splitForOneEmailInfoPerRecipient(List<EmailInfo> emailInfos);

    void writeToMsgFile(boolean isExportAsEml,
                        EmailInfo emailInfo,
                        File file,
                        EmailInfoBuilderProperties emailInfoBuilderProperties) throws IOException;

}