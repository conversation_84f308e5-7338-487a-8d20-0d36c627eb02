package cn.bluesking.nanfeng.tools.outlook;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import cn.bluesking.nanfeng.tools.common.utils.CompressedFileUtils;
import cn.bluesking.nanfeng.tools.common.utils.FileDownloadUtils;
import cn.bluesking.nanfeng.tools.common.utils.UuidGenerator;
import cn.bluesking.nanfeng.tools.config.properties.CoreProperties;

import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

/**
 * 电子信息相关控制器。
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping({ "/outlook/emailInfo", "/emailInfo" })
public class EmailInfoController {

    private static final Logger logger = LoggerFactory.getLogger(EmailInfoController.class);

    private static final String TMPDIR = System.getProperty("java.io.tmpdir");

    private static final ExecutorService executor;

    private static final ConcurrentHashMap<String, ProgressTracker> progressTrackers = new ConcurrentHashMap<>();

    // 用于存储异步任务的结果文件路径
    private static final ConcurrentHashMap<String, String> taskResults = new ConcurrentHashMap<>();

    static {

        // 优化线程池配置，减少线程数，增加队列容量。
        int cpuCount = Runtime.getRuntime().availableProcessors();
        // 至少 16 个核心线程
        int coreThreads = Math.max(cpuCount * 2, 16);
        // 至少 32 个最大线程
        int maxThreads = Math.max(cpuCount * 4, 32);

        executor = new ThreadPoolExecutor(
            coreThreads,
            maxThreads,
            60,
            TimeUnit.SECONDS,
            // 使用 LinkedBlockingQueue 增加队列容量
            new LinkedBlockingQueue<>(2000),
            new ThreadFactory() {
                private final AtomicInteger index = new AtomicInteger(0);

                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r, "emailInfo-" + index.incrementAndGet());
                    // 设置为守护线程，避免因线程池未关闭而阻止 JVM 退出。
                    thread.setDaemon(true);
                    return thread;
                }

            },
            // 使用 CallerRunsPolicy 避免任务被拒绝
            new ThreadPoolExecutor.CallerRunsPolicy());
    }

    private final EmailInfoService emailInfoService;
    private final CoreProperties coreProperties;

    public EmailInfoController(EmailInfoService emailInfoService, CoreProperties coreProperties) {
        this.emailInfoService = emailInfoService;
        this.coreProperties = coreProperties;
    }

    @GetMapping("/progress/{taskId}")
    @ResponseBody
    public Flux<ServerSentEvent<String>> getProgress(@PathVariable String taskId) {
        ProgressTracker tracker = progressTrackers.get(taskId);
        if (tracker == null) {
            return Flux.just(ServerSentEvent.<String>builder()
                                 .event("error")
                                 .data("任务不存在或已过期")
                                 .build());
        }

        // 将更新间隔从 100 毫秒增加到 500 毫秒，减少事件发送频率。
        return Flux.interval(Duration.ofMillis(500))
                   // 使用 publishOn 确保背压处理在单独的线程上执行
                   .publishOn(Schedulers.boundedElastic())
                   // 使用简单的背压缓冲区策略
                   .onBackpressureBuffer(32,
                       dropped -> logger.debug("背压缓冲区溢出，丢弃信号: {}", dropped))
                   .map(sequence -> {
                       ProgressInfo progress = tracker.getProgress();
                       return ServerSentEvent.<String>builder()
                                  .id(String.valueOf(sequence))
                                  .event("progress")
                                  .data(progress.toString())
                                  .build();
                   })
                   .takeUntil(sse -> tracker.isCompleted())
                   // 增加 take 操作符，限制最大事件数
                   .take(300)
                   .doOnCancel(() -> logger.debug("客户端取消了 SSE 连接: {}", taskId))
                   .doOnError(e -> logger.error("SSE 连接错误: {}", e.getMessage()))
                   .onErrorResume(e -> {
                       logger.error("处理 SSE 连接错误", e);
                       return Flux.just(ServerSentEvent.<String>builder()
                                            .event("error")
                                            .data("连接错误: " + e.getMessage())
                                            .build());
                   })
                   .concatWith(Flux.just(ServerSentEvent.<String>builder()
                                             .event("complete")
                                             .data("任务完成")
                                             .build()));
    }

    /**
     * 异步提交任务接口，立即返回任务ID。
     */
    @PostMapping("/submitGenerate")
    @ResponseBody
    public ResponseEntity<TaskInfo> submitGenerate(
        @RequestParam(defaultValue = "false") boolean isExportAsEml,
        @RequestParam(defaultValue = "false") boolean isOneEmailPerRecipient,
        String subjectTemplate,
        String contentTemplate,
        @RequestParam("recipientInfosFile") MultipartFile recipientInfosMultipartFile,
        @RequestParam(value = "msgTemplateFile", required = false) MultipartFile msgTemplateMultipartFile)
        throws IOException {

        String taskId = UuidGenerator.generate();
        ProgressTracker tracker = new ProgressTracker();
        progressTrackers.put(taskId, tracker);

        // 保存文件到临时目录
        String uuid = UuidGenerator.generate();
        File recipientInfosFile = transferTo(uuid, recipientInfosMultipartFile);
        File msgTemplateFile = transferTo(uuid, msgTemplateMultipartFile);

        // 异步执行生成任务
        executor.submit(() -> {
            try {
                File finalZipOutput = generateEmailFiles(
                    isExportAsEml,
                    isOneEmailPerRecipient,
                    subjectTemplate,
                    contentTemplate,
                    recipientInfosFile,
                    msgTemplateFile,
                    uuid,
                    tracker);

                // 将结果文件路径保存，以便后续下载
                taskResults.put(taskId, finalZipOutput.getAbsolutePath());

                // 设置任务完成状态
                tracker.updateProgress(100, "文件生成完成！");

                // 延迟 30 分钟后清理资源
                scheduleCleanup(taskId, recipientInfosFile, msgTemplateFile, uuid, 30);
            }

            catch (Exception e) {
                logger.error("生成邮件文件异常", e);
                tracker.updateError("生成邮件文件失败: " + e.getMessage());
                cleanupResources(taskId, recipientInfosFile, msgTemplateFile, uuid);
            }

        });

        // 立即返回任务 ID
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        return ResponseEntity.ok()
                   .headers(headers)
                   .body(new TaskInfo(taskId, "任务已提交，正在处理中..."));
    }

    /**
     * 获取异步任务的结果文件信息
     */
    @GetMapping("/getGenerateResult/{taskId}")
    @ResponseBody
    public FileInfo getGenerateResult(@PathVariable String taskId) {

        // 检查任务是否存在且完成
        ProgressTracker tracker = progressTrackers.get(taskId);
        if (tracker == null) {
            return new FileInfo(false, "任务不存在");
        }

        if (!tracker.isCompleted()) {
            return new FileInfo(false, "任务正在处理中");
        }

        // 获取结果文件路径
        String filePath = taskResults.get(taskId);
        if (filePath == null) {
            return new FileInfo(false, "文件不存在");
        }

        File resultFile = new File(filePath);
        if (!resultFile.exists()) {
            return new FileInfo(false, "文件不存在");
        }

        String filename = generateFileNameWithDate("生成邮件文件");

        // 构建完整的下载URL，使用配置的serverUrl
        String serverUrl = coreProperties.getServerUrl();
        String downloadPath = "/outlook/emailInfo/downloadFile/" + taskId;

        // 确保serverUrl末尾没有斜杠，而downloadPath开头有斜杠
        if (serverUrl.endsWith("/")) {
            serverUrl = serverUrl.substring(0, serverUrl.length() - 1);
        }

        if (!downloadPath.startsWith("/")) {
            downloadPath = "/" + downloadPath;
        }

        String downloadUrl = serverUrl + downloadPath;

        return new FileInfo(true, "文件准备就绪", taskId, filename, downloadUrl);
    }

    /**
     * 下载生成的文件，下载完成后将删除临时文件。
     */
    @GetMapping("/downloadFile/{taskId}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String taskId) throws IOException {
        // 获取结果文件路径
        String filePath = taskResults.get(taskId);
        if (filePath == null) {
            return ResponseEntity.notFound().build();
        }

        File resultFile = new File(filePath);
        if (!resultFile.exists()) {
            return ResponseEntity.notFound().build();
        }

        // 返回文件
        HttpHeaders headers = new HttpHeaders();
        String filename = generateFileNameWithDate("生成邮件文件");

        FileDownloadUtils.setContentDispositionHeader(headers, filename);

        // 创建输入流资源，在完成下载后会安排删除原文件
        InputStreamResource resource = new InputStreamResource(new FileInputStream(resultFile));

        // 安排文件下载完成后删除临时文件
        // 注意：这种方式并不能保证文件一定被删除，但这是最好的尝试
        // 更可靠的方式是使用定时任务清理过期文件
        // 不在这里移除任务结果，而是在文件实际删除时移除
        final String taskIdCopy = taskId;
        CompletableFuture.runAsync(() -> {
            try {

                // 添加 30 秒延迟，确保下载有时间完成。
                Thread.sleep(30000);
                if (resultFile.exists()) {
                    boolean deleted = FileUtils.deleteQuietly(resultFile);
                    if (deleted) {
                        logger.info("临时文件已删除：{}", resultFile.getAbsolutePath());
                        // 在文件实际删除后才移除任务结果
                        taskResults.remove(taskIdCopy);
                    }

                    else {
                        logger.warn("无法删除临时文件: {}", resultFile.getAbsolutePath());
                    }

                }

            }

            catch (Exception e) {
                logger.error("删除临时文件失败", e);
            }

        });

        return ResponseEntity.ok()
                   .headers(headers)
                   .contentLength(resultFile.length())
                   .contentType(MediaType.APPLICATION_OCTET_STREAM)
                   .body(resource);
    }

    /**
     * 原有的同步处理接口，保持不变。
     */
    @PostMapping("/generate")
    public ResponseEntity<Resource> generate(
        @RequestParam(defaultValue = "false") boolean isExportAsEml,
        @RequestParam(defaultValue = "false") boolean isOneEmailPerRecipient,
        String subjectTemplate,
        String contentTemplate,
        @RequestParam("recipientInfosFile") MultipartFile recipientInfosMultipartFile,
        @RequestParam(value = "msgTemplateFile", required = false) MultipartFile msgTemplateMultipartFile)
        throws IOException {

        String taskId = UuidGenerator.generate();
        ProgressTracker tracker = new ProgressTracker();
        progressTrackers.put(taskId, tracker);

        String uuid = UuidGenerator.generate();
        File recipientInfosFile = transferTo(uuid, recipientInfosMultipartFile);
        File msgTemplateFile = transferTo(uuid, msgTemplateMultipartFile);

        try {
            // 使用抽取出来的共用生成方法
            File finalZipOutput = generateEmailFiles(
                isExportAsEml,
                isOneEmailPerRecipient,
                subjectTemplate,
                contentTemplate,
                recipientInfosFile,
                msgTemplateFile,
                uuid,
                tracker);

            HttpHeaders headers = new HttpHeaders();
            String filename = generateFileNameWithDate("生成邮件文件");

            FileDownloadUtils.setContentDispositionHeader(headers, filename);
            headers.set("X-Task-Id", taskId);

            InputStreamResource resource = new InputStreamResource(new FileInputStream(finalZipOutput));
            return ResponseEntity.ok()
                       .headers(headers)
                       .contentLength(finalZipOutput.length())
                       .contentType(MediaType.APPLICATION_OCTET_STREAM)
                       .body(resource);
        }

        finally {
            // 延迟 5 秒后清理，确保前端有足够时间获取进度更新。
            scheduleCleanup(taskId, recipientInfosFile, msgTemplateFile, uuid, 1);
        }

    }

    /**
     * 调度延迟清理任务
     */
    private void scheduleCleanup(String taskId, File recipientInfosFile, File msgTemplateFile, String uuid,
                                 int delayMinutes) {
        CompletableFuture.delayedExecutor(delayMinutes, TimeUnit.MINUTES).execute(() -> {
            try {
                cleanupResources(taskId, recipientInfosFile, msgTemplateFile, uuid);
            }

            catch (Exception e) {
                logger.warn("清理资源时发生异常", e);
            }

        });
    }

    /**
     * 清理资源
     */
    private void cleanupResources(String taskId, File recipientInfosFile, File msgTemplateFile, String uuid) {

        progressTrackers.remove(taskId);

        // 先检查这个任务是否还有结果文件，如果有则保留，可能正在被下载
        String filePath = taskResults.get(taskId);
        if (filePath != null) {
            File resultFile = new File(filePath);
            if (resultFile.exists()) {
                logger.info("任务 {} 的结果文件还存在，暂不从 taskResults 中移除", taskId);
            }

            else {
                // 文件不存在才从 taskResults 中移除
                taskResults.remove(taskId);
                logger.info("任务 {} 的结果文件不存在，从 taskResults 中移除", taskId);
            }

        }

        else {
            // 如果 taskResults 中没有，直接移除。
            taskResults.remove(taskId);
        }

        FileUtils.deleteQuietly(recipientInfosFile);
        FileUtils.deleteQuietly(msgTemplateFile);

        File zipOutputDir = new File(TMPDIR + uuid + File.separator);
        try {
            FileUtils.cleanDirectory(zipOutputDir);
            FileUtils.deleteDirectory(zipOutputDir);
            FileUtils.deleteQuietly(new File(TMPDIR + "email_files_" + uuid + ".zip"));
        }

        catch (IOException e) {
            logger.warn("清理临时文件失败", e);
        }

    }

    /**
     * 生成邮件文件，抽取为公共方法。
     */
    private File generateEmailFiles(
        boolean isExportAsEml,
        boolean isOneEmailPerRecipient,
        String subjectTemplate,
        String contentTemplate,
        File recipientInfosFile,
        File msgTemplateFile,
        String uuid,
        ProgressTracker tracker) throws IOException {

        File zipOutputDir = new File(TMPDIR + uuid + File.separator);
        FileUtils.forceMkdir(zipOutputDir);

        File finalZipOutput = new File(TMPDIR + "email_files_" + uuid + "_" +
                                           java.time.LocalDateTime.now()
                                               .format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"))
                                           + ".zip");

        tracker.updateProgress(0, "开始处理文件...");

        List<File> zipOutputs = new ArrayList<>();
        String outputDirPath = StringUtils.appendIfMissing(zipOutputDir.getAbsolutePath(), File.separator);

        List<SheetEmailInfo> sheetEmailInfos = emailInfoService.readFromExcel(recipientInfosFile);
        tracker.updateProgress(10, "Excel 文件读取完成");

        EmailInfoBuilderProperties emailInfoBuilderProperties = emailInfoService.readRefineEmailInfoProperties(
            subjectTemplate, contentTemplate, msgTemplateFile);
        tracker.updateProgress(20, "模板文件处理完成");

        int index = 0;
        int log10 = (int) Math.log10(Math.max(sheetEmailInfos.size(), 1)) + 1;
        List<Future<?>> futures = new ArrayList<>();
        AtomicLong processedCount = new AtomicLong(0);

        // 计算总处理项目数量
        long totalCount = calcTotalCount(isOneEmailPerRecipient, sheetEmailInfos);

        // 批处理优化 - 对大型工作表进行分批处理
        int batchSize = 50; // 每批处理的邮件数量上限

        for (SheetEmailInfo sheetEmailInfo : sheetEmailInfos) {
            String sheetName = sheetEmailInfo.getSheetName();
            List<EmailInfo> emailInfos = sheetEmailInfo.getEmailInfos();

            if (isOneEmailPerRecipient) {
                emailInfos = emailInfoService.splitForOneEmailInfoPerRecipient(emailInfos);
            }

            // 对大型列表进行分批，减少单个Future的工作量
            if (emailInfos.size() > batchSize) {
                int batches = (int) Math.ceil(emailInfos.size() / (double) batchSize);
                for (int i = 0; i < batches; i++) {
                    int startIndex = i * batchSize;
                    int endIndex = Math.min((i + 1) * batchSize, emailInfos.size());
                    List<EmailInfo> batchList = emailInfos.subList(startIndex, endIndex);

                    String batchOrderNo = String.format("%0" + log10 + "d", ++index);
                    String batchName = i == 0 ? sheetName : sheetName + "_batch" + (i + 1);
                    String zipOutputName = outputDirPath + batchOrderNo + "-" + formatFileName(batchName) + ".zip";
                    File zipOutput = new File(zipOutputName);

                    processBatch(isExportAsEml, isOneEmailPerRecipient, batchList, zipOutput,
                        emailInfoBuilderProperties, tracker, processedCount, totalCount, futures);
                    zipOutputs.add(zipOutput);
                }

            }

            else {
                String orderNo = String.format("%0" + log10 + "d", ++index);
                String zipOutputName = outputDirPath + orderNo + "-" + formatFileName(sheetName) + ".zip";
                File zipOutput = new File(zipOutputName);

                processBatch(isExportAsEml, isOneEmailPerRecipient, emailInfos, zipOutput,
                    emailInfoBuilderProperties, tracker, processedCount, totalCount, futures);
                zipOutputs.add(zipOutput);
            }

        }

        waitForAllFinish(futures);
        tracker.updateProgress(90, "正在压缩文件...");
        CompressedFileUtils.zipFile(zipOutputs, finalZipOutput);
        tracker.updateProgress(100, "文件生成完成！");

        return finalZipOutput;
    }

    /**
     * 处理一批邮件信息
     */
    private void processBatch(
        boolean isExportAsEml,
        boolean isOneEmailPerRecipient,
        List<EmailInfo> emailInfos,
        File zipOutput,
        EmailInfoBuilderProperties properties,
        ProgressTracker tracker,
        AtomicLong processedCount,
        long totalCount,
        List<Future<?>> futures) {

        futures.add(executor.submit(() -> {
            try {
                writeZipFile(isExportAsEml, isOneEmailPerRecipient, emailInfos, zipOutput, properties, tracker,
                    processedCount, totalCount);

                logger.debug("finish to write to zip file[{}]!", zipOutput.getName());
            }

            catch (IOException e) {
                throw new RuntimeException(e);
            }

        }));
    }

    private static long calcTotalCount(boolean isOneEmailPerRecipient, List<SheetEmailInfo> sheetEmailInfos) {

        long totalCount = sheetEmailInfos.stream()
                              .mapToLong(sheet -> isOneEmailPerRecipient
                                                      ? sheet.getEmailInfos().stream()
                                                            .mapToLong(email -> email.getRecipientInfos().size())
                                                            .sum()
                                                      : sheet.getEmailInfos().size())
                              .sum();

        if (totalCount == 0) {
            totalCount = 1; // 防止除零错误
        }

        return totalCount;
    }

    private File transferTo(String uuid, MultipartFile multipartFile) throws IOException {

        if (multipartFile == null) {
            return null;
        }

        else {

            String inputFilePath = TMPDIR + uuid + "-" + multipartFile.getOriginalFilename();
            File file = new File(inputFilePath);
            multipartFile.transferTo(file);
            return file;
        }

    }

    @SuppressWarnings("java:S3457")
    private void writeZipFile(boolean isExportAsEml,
                              boolean isOneEmailPerRecipient,
                              List<EmailInfo> emailInfos,
                              File zipOutput,
                              EmailInfoBuilderProperties emailInfoBuilderProperties) throws IOException {
        // 使用默认参数调用新方法
        writeZipFile(isExportAsEml, isOneEmailPerRecipient, emailInfos, zipOutput,
            emailInfoBuilderProperties, null, null, 0);
    }

    @SuppressWarnings("java:S3457")
    private void writeZipFile(boolean isExportAsEml,
                              boolean isOneEmailPerRecipient,
                              List<EmailInfo> emailInfos,
                              File zipOutput,
                              EmailInfoBuilderProperties emailInfoBuilderProperties,
                              ProgressTracker tracker,
                              AtomicLong processedCount,
                              long totalCount) throws IOException {

        File outputDir = new File(TMPDIR + UuidGenerator.generate() + File.separator);
        FileUtils.forceMkdir(outputDir);

        try {
            String outputDirPath = StringUtils.appendIfMissing(outputDir.getAbsolutePath(), File.separator);
            int log10 = (int) Math.log10(Math.max(emailInfos.size(), 1)) + 1;
            List<Future<?>> futures = new ArrayList<>();
            List<File> outputs = Collections.synchronizedList(new ArrayList<>(emailInfos.size()));

            // 分批处理提交任务，避免一次性提交过多任务
            int batchSize = 20; // 每批提交的任务数

            for (int batchStart = 0; batchStart < emailInfos.size(); batchStart += batchSize) {
                int batchEnd = Math.min(batchStart + batchSize, emailInfos.size());

                // 提交一批任务
                for (int i = batchStart; i < batchEnd; i++) {
                    EmailInfo emailInfo = emailInfos.get(i);

                    String orderNo = String.format("%0" + log10 + "d", i + 1);
                    String fileNameSuffix = isExportAsEml ? ".emltpl" : ".msg";
                    String outputFileName =
                        orderNo + "-" + getFileName(isOneEmailPerRecipient, emailInfo) + fileNameSuffix;
                    File output = new File(outputDirPath + outputFileName);

                    futures.add(executor.submit(() -> {
                        try {
                            // 写入邮件文件
                            emailInfoService.writeToMsgFile(isExportAsEml, emailInfo, output,
                                emailInfoBuilderProperties);
                            logger.debug("success to write to msg file[{}]!", outputFileName);

                            // 文件写入完成后更新进度（如果提供了进度跟踪器）
                            if (tracker != null && processedCount != null) {
                                long currentProcessed = processedCount.incrementAndGet();

                                // 计算进度百分比：基础进度(20%) + 处理邮件的进度(70%)
                                int progress = (int) ((currentProcessed * 70.0 / totalCount) + 20);

                                // 确保不超过90%（最后10%留给压缩文件步骤）
                                progress = Math.min(progress, 90);

                                tracker.updateProgress(progress,
                                    String.format("正在处理第 %d/%d 个邮件 (%.1f%%)",
                                        currentProcessed, totalCount, (currentProcessed * 100.0 / totalCount)));
                            }

                        }

                        catch (IOException e) {
                            throw new RuntimeException(e);
                        }

                    }));
                    outputs.add(output);
                }

                // 如果不是最后一批，并且任务比较多，等待当前批次完成一部分再继续
                if (batchEnd < emailInfos.size() && emailInfos.size() > batchSize * 2) {
                    try {
                        // 仅等待一小段时间，而不是等待所有完成
                        Thread.sleep(100);
                    }

                    catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }

                }

            }

            waitForAllFinish(futures);
            CompressedFileUtils.zipFile(outputs, zipOutput);
        }

        finally {
            // MacOS里可能会有一些隐藏文件（例如：.DS_Store）
            FileUtils.cleanDirectory(outputDir);
            FileUtils.deleteDirectory(outputDir);
        }

    }

    private void waitForAllFinish(List<Future<?>> futures) {
        // 批量等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get();
            }

            catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("任务等待被中断", e);
            }

            catch (ExecutionException e) {
                throw new RuntimeException("任务执行失败", e.getCause());
            }

        }

    }

    private static String getFileName(boolean isOneEmailPerRecipient, EmailInfo emailInfo) {

        String fileName = isOneEmailPerRecipient ? getFirstRecipientName(emailInfo) : emailInfo.getCompanyName();
        return formatFileName(fileName);
    }

    private static String formatFileName(String fileName) {
        return fileName.replaceAll("[\\s\\\\/:\\*\\?\"<>|]", "");
    }

    private static String getFirstRecipientName(EmailInfo emailInfo) {

        return Optional.ofNullable(emailInfo)
                   .map(EmailInfo::getRecipientInfos)
                   .orElseGet(Collections::emptyList)
                   .stream()
                   .map(EmailInfo.RecipientInfo::getEmailAddress)
                   .filter(StringUtils::isNotEmpty)
                   .findFirst()
                   .orElseThrow();
    }

    /**
     * 生成带有日期时间的文件名
     *
     * @param baseName 基础文件名
     * @return 带有日期时间的文件名，格式为：基础文件名_yyyyMMdd_HHmmss.zip。
     */
    private String generateFileNameWithDate(String baseName) {
        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
        String dateStr = java.time.LocalDateTime.now().format(formatter);

        // 对中文基础文件名使用拼音或英文替代，避免编码问题
        if ("生成邮件文件".equals(baseName)) {
            baseName = "email_files";
        }

        return baseName + "_" + dateStr + ".zip";
    }

    private static class ProgressTracker {
        private volatile int progress = 0;
        private volatile String message = "";
        private volatile boolean completed = false;
        private volatile boolean error = false;

        // 添加更新时间戳，用于检测任务是否活跃
        private volatile long lastUpdateTime = System.currentTimeMillis();

        public synchronized void updateProgress(int progress, String message) {
            this.progress = progress;
            this.message = message;
            this.lastUpdateTime = System.currentTimeMillis();
            if (progress >= 100) {
                this.completed = true;
            }

            logger.debug("更新进度: {}%, {}", progress, message);
        }

        public synchronized void updateError(String errorMessage) {
            this.message = errorMessage;
            this.error = true;
            this.completed = true;
            this.lastUpdateTime = System.currentTimeMillis();
            logger.error("任务出错: {}", errorMessage);
        }

        public synchronized ProgressInfo getProgress() {
            return new ProgressInfo(progress, message, error);
        }

        public synchronized boolean isCompleted() {
            return completed;
        }

        public synchronized boolean hasError() {
            return error;
        }

        public synchronized boolean isStale(long timeoutMillis) {
            return !completed && (System.currentTimeMillis() - lastUpdateTime > timeoutMillis);
        }

    }

    private static class ProgressInfo {

        private final int progress;
        private final String message;
        private final boolean error;

        public ProgressInfo(int progress, String message, boolean error) {
            this.progress = progress;
            this.message = message;
            this.error = error;
        }

        @Override
        public String toString() {
            return String.format("{\"progress\":%d,\"message\":\"%s\",\"error\":%b}",
                progress, message, error);
        }

    }

    /**
     * 任务信息响应对象
     */
    public static class TaskInfo {
        private final String taskId;
        private final String message;

        public TaskInfo(String taskId, String message) {
            this.taskId = taskId;
            this.message = message;
        }

        public String getTaskId() {
            return taskId;
        }

        public String getMessage() {
            return message;
        }

    }

    /**
     * 文件信息响应对象
     */
    public static class FileInfo {

        private final boolean ready;
        private final String message;
        private String taskId;
        private String filename;
        private String downloadUrl;

        public FileInfo(boolean ready, String message) {
            this.ready = ready;
            this.message = message;
        }

        public FileInfo(boolean ready, String message, String taskId, String filename) {
            this.ready = ready;
            this.message = message;
            this.taskId = taskId;
            this.filename = filename;
        }

        public FileInfo(boolean ready, String message, String taskId, String filename, String downloadUrl) {
            this.ready = ready;
            this.message = message;
            this.taskId = taskId;
            this.filename = filename;
            this.downloadUrl = downloadUrl;
        }

        public boolean isReady() {
            return ready;
        }

        public String getMessage() {
            return message;
        }

        public String getTaskId() {
            return taskId;
        }

        public String getFilename() {
            return filename;
        }

        public String getDownloadUrl() {
            return downloadUrl;
        }

    }

}