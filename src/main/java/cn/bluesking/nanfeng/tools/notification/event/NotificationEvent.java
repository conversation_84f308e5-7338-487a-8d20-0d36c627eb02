package cn.bluesking.nanfeng.tools.notification.event;

import java.util.List;

import cn.bluesking.nanfeng.tools.notification.template.MailTemplate;

/**
 * 通知事件基类，用于异步发送通知。
 *
 * <AUTHOR>
 */
public abstract class NotificationEvent {

    /**
     * 收件人邮箱地址列表。
     */
    private final List<String> toList;

    /**
     * 邮件模板。
     */
    private final MailTemplate template;

    /**
     * 构造函数。
     *
     * @param toList 收件人邮箱地址列表
     * @param template 邮件模板
     */
    protected NotificationEvent(List<String> toList, MailTemplate template) {
        this.toList = toList;
        this.template = template;
    }

    /**
     * 获取收件人邮箱地址列表。
     *
     * @return 收件人邮箱地址列表
     */
    public List<String> getToList() {
        return toList;
    }

    /**
     * 获取邮件模板。
     *
     * @return 邮件模板
     */
    public MailTemplate getTemplate() {
        return template;
    }

}