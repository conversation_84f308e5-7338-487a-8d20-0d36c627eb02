package cn.bluesking.nanfeng.tools.notification.template.factory;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.bluesking.nanfeng.tools.config.properties.NotificationMailProperties;
import cn.bluesking.nanfeng.tools.notification.template.AbstractMailTemplate;
import cn.bluesking.nanfeng.tools.notification.template.RedirectMailboxCreatedTemplate;

/**
 * 邮件模板工厂类，用于创建各种邮件模板实例。 工厂会自动从配置文件中读取 LOGO 相关配置，应用到所有创建的模板上。
 *
 * <AUTHOR>
 */
@Component
public class MailTemplateFactory {

    /**
     * 邮件通知配置属性。
     */
    private final NotificationMailProperties mailProperties;

    /**
     * 构造函数。
     *
     * @param mailProperties 邮件通知配置属性
     */
    @Autowired
    public MailTemplateFactory(NotificationMailProperties mailProperties) {
        this.mailProperties = mailProperties;
    }

    /**
     * 创建重定向邮箱创建成功通知的邮件模板。
     *
     * @param mailboxName 创建的邮箱名称
     * @param mailboxAddress 邮箱完整地址
     * @param redirectToAddresses 转发目标邮箱地址列表
     * @return 重定向邮箱创建成功通知的邮件模板
     */
    public RedirectMailboxCreatedTemplate createRedirectMailboxCreatedTemplate(String mailboxName,
                                                                               String mailboxAddress,
                                                                               List<String> redirectToAddresses) {

        RedirectMailboxCreatedTemplate template = new RedirectMailboxCreatedTemplate(mailboxName, mailboxAddress,
            redirectToAddresses);

        // 应用配置属性中的 LOGO 设置
        return applyLogoSettings(template);
    }

    /**
     * 将配置属性中的 LOGO URL 应用到邮件模板上。
     *
     * @param template 邮件模板
     * @param <T> 邮件模板类型
     * @return 应用了 LOGO URL 的邮件模板
     */
    private <T extends AbstractMailTemplate> T applyLogoSettings(T template) {
        template.setLogoUrl(mailProperties.getLogoUrl());
        return template;
    }

}