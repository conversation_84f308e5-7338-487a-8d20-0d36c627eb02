package cn.bluesking.nanfeng.tools.notification.template;

import java.util.List;

/**
 * 重定向邮箱创建成功通知的邮件模板。
 *
 * <AUTHOR>
 */
public class RedirectMailboxCreatedTemplate extends AbstractMailTemplate {

    /**
     * 创建的邮箱名称。
     */
    private final String mailboxName;

    /**
     * 邮箱完整地址。
     */
    private final String mailboxAddress;

    /**
     * 转发目标邮箱地址列表。
     */
    private final List<String> redirectToAddresses;

    /**
     * 默认的 LOGO URL。
     */
    private static final String DEFAULT_LOGO_URL = "https://nanfeng-tools.bluesking.cn/assets/images/logo.png";

    /**
     * 构造函数。
     *
     * @param mailboxName 创建的邮箱名称
     * @param mailboxAddress 邮箱完整地址
     * @param redirectToAddresses 转发目标邮箱地址列表
     */
    public RedirectMailboxCreatedTemplate(String mailboxName, String mailboxAddress, List<String> redirectToAddresses) {
        super(MailTemplateType.REDIRECT_MAILBOX_CREATED, "重定向邮箱创建成功通知");
        this.mailboxName = mailboxName;
        this.mailboxAddress = mailboxAddress;
        this.redirectToAddresses = redirectToAddresses;

        // 默认设置 LOGO URL
        this.setLogoUrl(DEFAULT_LOGO_URL);
    }

    /**
     * 构造函数，允许指定自定义 LOGO URL。
     *
     * @param mailboxName 创建的邮箱名称
     * @param mailboxAddress 邮箱完整地址
     * @param redirectToAddresses 转发目标邮箱地址列表
     * @param logoUrl 自定义 LOGO URL，如果为 null 则使用默认 LOGO。
     */
    public RedirectMailboxCreatedTemplate(String mailboxName, String mailboxAddress, List<String> redirectToAddresses, String logoUrl) {
        this(mailboxName, mailboxAddress, redirectToAddresses);
        if (logoUrl != null && !logoUrl.trim().isEmpty()) {
            this.setLogoUrl(logoUrl);
        }

    }

    @Override
    public String getContent() {
        StringBuilder contentBuilder = new StringBuilder();
        contentBuilder.append("<div style=\"padding: 20px 0;\">\n")
                      .append("  <p>您好，</p>\n")
                      .append("  <p>您的重定向邮箱已成功创建。以下是邮箱详情：</p>\n")
                      .append("  <div style=\"background-color: #f8f9fa; padding: 15px; margin: 15px 0; border-left: 4px solid #4C9DAD;\">\n")
                      .append("    <p><strong>邮箱地址：</strong>").append(mailboxAddress).append("</p>\n");

        if (redirectToAddresses != null && !redirectToAddresses.isEmpty()) {
            contentBuilder.append("    <p><strong>转发目标：</strong></p>\n")
                          .append("    <ul style=\"margin-top: 5px;\">\n");

            for (String address : redirectToAddresses) {
                contentBuilder.append("      <li>").append(address).append("</li>\n");
            }

            contentBuilder.append("    </ul>\n");
        }

        contentBuilder.append("  </div>\n")
                      .append("  <p>现在，发送到此邮箱的所有邮件将自动转发到以上列出的目标邮箱地址。</p>\n")
                      .append("  <p>注意：邮箱名称一旦创建成功，就不能修改或删除。</p>\n")
                      .append("  <p>感谢您使用南风工具箱！</p>\n")
                      .append("</div>");

        return wrapContent(contentBuilder.toString());
    }

    /**
     * 获取创建的邮箱名称。
     *
     * @return 邮箱名称
     */
    public String getMailboxName() {
        return mailboxName;
    }

    /**
     * 获取邮箱完整地址。
     *
     * @return 邮箱完整地址
     */
    public String getMailboxAddress() {
        return mailboxAddress;
    }

    /**
     * 获取转发目标邮箱地址列表。
     *
     * @return 转发目标邮箱地址列表
     */
    public List<String> getRedirectToAddresses() {
        return redirectToAddresses;
    }

}