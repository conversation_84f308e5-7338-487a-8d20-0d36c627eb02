package cn.bluesking.nanfeng.tools.notification.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import cn.bluesking.nanfeng.tools.notification.event.NotificationEvent;
import cn.bluesking.nanfeng.tools.notification.service.NotificationService;

/**
 * 通知事件监听器，用于监听和处理通知事件。
 *
 * <AUTHOR>
 */
@Component
class NotificationEventListener {

    private static final Logger logger = LoggerFactory.getLogger(NotificationEventListener.class);

    private final NotificationService notificationService;

    /**
     * 构造函数。
     *
     * @param notificationService 通知服务
     */
    public NotificationEventListener(NotificationService notificationService) {
        this.notificationService = notificationService;
    }

    /**
     * 监听通知事件，异步处理。
     *
     * @param event 通知事件
     */
    @Async
    @EventListener
    public void onNotificationEvent(NotificationEvent event) {

        logger.info("received notification event: {}", event.getClass().getSimpleName());
        try {

            boolean success = notificationService.sendMail(event.getToList(), event.getTemplate());
            if (success) {
                logger.info("notification sent successfully.");
            }
            else {
                logger.warn("failed to send notification!");
            }
        }
        catch (Exception e) {
            logger.error("error while processing notification event: {}", e.getMessage(), e);
        }

    }

}