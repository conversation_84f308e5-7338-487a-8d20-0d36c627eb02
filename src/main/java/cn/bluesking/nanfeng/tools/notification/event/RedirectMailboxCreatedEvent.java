package cn.bluesking.nanfeng.tools.notification.event;

import java.util.List;

import cn.bluesking.nanfeng.tools.notification.template.MailTemplate;
import cn.bluesking.nanfeng.tools.notification.template.RedirectMailboxCreatedTemplate;

/**
 * 重定向邮箱创建成功通知事件。
 *
 * <AUTHOR>
 */
public class RedirectMailboxCreatedEvent extends NotificationEvent {

    /**
     * 构造函数。
     *
     * @param toList 收件人邮箱地址列表
     * @param template 邮件模板，必须是 RedirectMailboxCreatedTemplate 类型。
     */
    public RedirectMailboxCreatedEvent(List<String> toList, RedirectMailboxCreatedTemplate template) {
        super(toList, template);
    }

    /**
     * 获取重定向邮箱创建成功通知模板。
     *
     * @return 重定向邮箱创建成功通知模板
     */
    @Override
    public RedirectMailboxCreatedTemplate getTemplate() {
        return (RedirectMailboxCreatedTemplate) super.getTemplate();
    }

}