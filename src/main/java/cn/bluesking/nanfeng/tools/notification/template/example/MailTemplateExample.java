package cn.bluesking.nanfeng.tools.notification.template.example;

import java.util.Arrays;
import java.util.List;

import cn.bluesking.nanfeng.tools.config.properties.NotificationMailProperties;
import cn.bluesking.nanfeng.tools.notification.template.RedirectMailboxCreatedTemplate;
import cn.bluesking.nanfeng.tools.notification.template.factory.MailTemplateFactory;

/**
 * 邮件模板使用示例类。 此类仅用于演示如何使用带有 LOGO 配置的邮件模板。
 *
 * <AUTHOR>
 */
public class MailTemplateExample {

    /**
     * 演示如何使用带有 LOGO 的邮件模板。
     */
    public void demonstrateMailTemplateWithLogo() {
        // 示例参数
        String mailboxName = "example";
        String mailboxAddress = "<EMAIL>";
        List<String> redirectToAddresses = Arrays.asList(
            "<EMAIL>",
            "<EMAIL>"
        );

        // 使用默认 LOGO 的模板
        RedirectMailboxCreatedTemplate template1 = new RedirectMailboxCreatedTemplate(
            mailboxName, mailboxAddress, redirectToAddresses);

        System.out.println("默认 LOGO 的模板 HTML:\n" + template1.getContent());

        // 使用自定义 LOGO URL 的模板
        RedirectMailboxCreatedTemplate template2 = new RedirectMailboxCreatedTemplate(
            mailboxName, mailboxAddress, redirectToAddresses,
            "https://custom-domain.com/custom-logo.png");

        System.out.println("自定义 LOGO 的模板 HTML:\n " + template2.getContent());
    }

    /**
     * 演示如何使用工厂和配置文件创建邮件模板。
     *
     * @param mailTemplateFactory 邮件模板工厂
     * @param mailProperties 邮件配置属性
     */
    public void demonstrateMailTemplateWithFactory(
        MailTemplateFactory mailTemplateFactory,
        NotificationMailProperties mailProperties) {

        // 示例参数
        String mailboxName = "example";
        String mailboxAddress = "<EMAIL>";
        List<String> redirectToAddresses = Arrays.asList(
            "<EMAIL>",
            "<EMAIL>"
        );

        System.out.println("=== 使用工厂和配置文件创建邮件模板 ===");
        System.out.println("配置文件中的 LOGO 设置：");
        System.out.println("- LOGO URL: " + mailProperties.getLogoUrl());

        // 使用工厂创建模板
        RedirectMailboxCreatedTemplate template = mailTemplateFactory.createRedirectMailboxCreatedTemplate(
            mailboxName, mailboxAddress, redirectToAddresses);

        System.out.println("\n使用工厂创建的模板 HTML:\n" + template.getContent());
    }

    /**
     * 主方法，用于运行示例。 注意：此方法仅用于演示，在实际应用中应该通过依赖注入获取 MailTemplateFactory。
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        MailTemplateExample example = new MailTemplateExample();
        example.demonstrateMailTemplateWithLogo();

        System.out.println("\n\n在实际应用中，您应该使用 Spring 依赖注入获取 MailTemplateFactory，" +
                               "它会自动从配置文件中读取 LOGO 设置。");
    }

}