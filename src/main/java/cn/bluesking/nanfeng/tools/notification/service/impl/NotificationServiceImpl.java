package cn.bluesking.nanfeng.tools.notification.service.impl;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Properties;

import jakarta.annotation.PostConstruct;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import cn.bluesking.nanfeng.tools.config.properties.NotificationMailProperties;
import cn.bluesking.nanfeng.tools.notification.service.NotificationService;
import cn.bluesking.nanfeng.tools.notification.template.MailTemplate;

/**
 * 通知服务实现类，使用 JavaMailSender 发送邮件。
 *
 * <AUTHOR>
 */
@Service
class NotificationServiceImpl implements NotificationService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationServiceImpl.class);

    private final NotificationMailProperties mailProperties;

    private JavaMailSender mailSender;

    /**
     * 构造函数。
     *
     * @param mailProperties 邮件配置属性
     */
    public NotificationServiceImpl(NotificationMailProperties mailProperties) {
        this.mailProperties = mailProperties;
    }

    /*-------------------- initialization method --------------------*/

    @PostConstruct
    public void init() {

        if (!mailProperties.isEnabled()) {
            logger.info("mail notification is disabled");
            return;
        }

        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();

        mailSender.setHost(mailProperties.getHost());
        mailSender.setPort(mailProperties.getPort());
        mailSender.setUsername(mailProperties.getUsername());
        mailSender.setPassword(mailProperties.getPassword());
        mailSender.setDefaultEncoding(StandardCharsets.UTF_8.name());

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");

        // 对于 587 端口，使用 STARTTLS。
        if (mailProperties.isTls()) {
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");
        }

        if (mailProperties.isSsl()) {
            props.put("mail.smtp.ssl.enable", "true");

            if (mailProperties.getPort() == 465) {
                props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
            }

        }

        props.put("mail.smtp.connectiontimeout", mailProperties.getConnectionTimeout());
        props.put("mail.smtp.timeout", mailProperties.getTimeout());
        // 生产环境关闭调试。
        props.put("mail.debug", "false");

        this.mailSender = mailSender;
        logger.info("mail sender initialized with host: {}, port: {}", mailProperties.getHost(),
            mailProperties.getPort());
    }

    /*-------------------- public method --------------------*/

    @Override
    public boolean sendMail(String to, MailTemplate template) {
        return sendMail(Collections.singletonList(to), null, template);
    }

    @Override
    public boolean sendMail(List<String> toList, MailTemplate template) {
        return sendMail(toList, null, template);
    }

    @Override
    public boolean sendMail(String to, String cc, MailTemplate template) {
        return sendMail(
            Collections.singletonList(to),
            cc != null ? Collections.singletonList(cc) : null,
            template
        );
    }

    @Override
    public boolean sendMail(List<String> toList, List<String> ccList, MailTemplate template) {

        if (!mailProperties.isEnabled()) {
            logger.info("mail notification is disabled, skip sending mail");
            return false;
        }

        if (mailSender == null) {
            logger.error("mail sender is not initialized");
            return false;
        }

        if (toList == null || toList.isEmpty()) {
            logger.error("recipient list is empty");
            return false;
        }

        try {

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, StandardCharsets.UTF_8.name());

            // 设置发件人。
            helper.setFrom(mailProperties.getFrom(), mailProperties.getFromName());

            // 设置收件人，支持多个收件人。
            String[] toArray = toList.toArray(new String[0]);
            helper.setTo(toArray);

            // 设置抄送人，如果有的话。
            if (ccList != null && !ccList.isEmpty()) {
                String[] ccArray = ccList.toArray(new String[0]);
                helper.setCc(ccArray);
            }

            // 设置邮件主题和内容。
            helper.setSubject(template.getSubject());
            helper.setText(template.getContent(), true);

            // 发送邮件。
            mailSender.send(message);

            logger.info("mail sent successfully to {}, template type: {}", String.join(", ", toList),
                template.getType());
            return true;
        }
        catch (UnsupportedEncodingException | MessagingException e) {
            logger.error("failed to send mail: {}", e.getMessage(), e);
            return false;
        }
    }

}