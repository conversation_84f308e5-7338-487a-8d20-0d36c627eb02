package cn.bluesking.nanfeng.tools.notification.service;

import java.util.List;

import cn.bluesking.nanfeng.tools.notification.template.MailTemplate;

/**
 * 通知服务接口，定义发送通知的方法。
 *
 * <AUTHOR>
 */
public interface NotificationService {

    /**
     * 发送邮件通知。
     *
     * @param to 收件人邮箱地址
     * @param template 邮件模板
     * @return 发送成功返回 true，否则返回 false。
     */
    boolean sendMail(String to, MailTemplate template);

    /**
     * 发送邮件通知给多个收件人。
     *
     * @param toList 收件人邮箱地址列表
     * @param template 邮件模板
     * @return 发送成功返回 true，否则返回 false。
     */
    boolean sendMail(List<String> toList, MailTemplate template);

    /**
     * 发送带抄送的邮件通知。
     *
     * @param to 收件人邮箱地址
     * @param cc 抄送人邮箱地址
     * @param template 邮件模板
     * @return 发送成功返回 true，否则返回 false。
     */
    boolean sendMail(String to, String cc, MailTemplate template);

    /**
     * 发送带抄送的邮件通知给多个收件人。
     *
     * @param toList 收件人邮箱地址列表
     * @param ccList 抄送人邮箱地址列表
     * @param template 邮件模板
     * @return 发送成功返回 true，否则返回 false。
     */
    boolean sendMail(List<String> toList, List<String> ccList, MailTemplate template);

}