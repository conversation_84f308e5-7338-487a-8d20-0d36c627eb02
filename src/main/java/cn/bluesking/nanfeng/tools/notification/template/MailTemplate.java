package cn.bluesking.nanfeng.tools.notification.template;

/**
 * 邮件模板接口，定义邮件模板的基本方法。
 *
 * <AUTHOR>
 */
public interface MailTemplate {

    /**
     * 获取邮件主题。
     *
     * @return 邮件主题
     */
    String getSubject();

    /**
     * 获取邮件内容。
     *
     * @return 邮件内容（HTML 格式）
     */
    String getContent();

    /**
     * 获取邮件模板类型。
     *
     * @return 邮件模板类型
     */
    MailTemplateType getType();

}