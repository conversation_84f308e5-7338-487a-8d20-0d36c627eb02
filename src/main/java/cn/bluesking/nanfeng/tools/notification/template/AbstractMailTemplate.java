package cn.bluesking.nanfeng.tools.notification.template;

/**
 * 抽象邮件模板类，实现一些通用功能。
 *
 * <AUTHOR>
 */
public abstract class AbstractMailTemplate implements MailTemplate {

    /**
     * 邮件模板类型。
     */
    private final MailTemplateType type;

    /**
     * 邮件主题。
     */
    private final String subject;

    /**
     * LOGO 的 URL。
     */
    private String logoUrl = "https://tools.bluesking.cn/assets/images/logo.png";

    /**
     * 构造函数。
     *
     * @param type 邮件模板类型
     * @param subject 邮件主题
     */
    protected AbstractMailTemplate(MailTemplateType type, String subject) {
        this.type = type;
        this.subject = subject;
    }

    @Override
    public String getSubject() {
        return subject;
    }

    @Override
    public MailTemplateType getType() {
        return type;
    }

    /**
     * 获取默认的页眉 HTML。
     *
     * @return 页眉 HTML
     */
    protected String getDefaultHeader() {
        StringBuilder headerBuilder = new StringBuilder();
        headerBuilder.append("<div style=\"background-color: #f5f5f5; padding: 20px; font-family: Arial, sans-serif;\">\n")
                    .append("  <div style=\"max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 30px; border-radius: 5px; box-shadow: 0px 0px 10px rgba(0,0,0,0.1);\">\n");

        // LOGO 与标题组合
        headerBuilder.append("    <div style=\"display: flex; align-items: center; margin-bottom: 20px;\">\n");

        // 如果有 LOGO，则显示 LOGO。
        if (logoUrl != null && !logoUrl.trim().isEmpty()) {
            headerBuilder.append("      <img src=\"").append(logoUrl).append("\" alt=\"南风工具箱\" style=\"max-width: 120px; height: auto; margin-right: 15px;\">\n");
        }

        // 添加标题和副标题，使用吉卜力风格的色系（温暖的蓝绿色）
        headerBuilder.append("      <div>\n")
                    .append("        <h1 style=\"margin: 0; color: #4C9DAD; font-size: 24px; font-weight: 700;\">南风工具箱</h1>\n")
                    .append("        <p style=\"margin: 5px 0 0; color: #6EA4B3; font-size: 14px; font-style: italic;\">NANFENG TOOLBOX</p>\n")
                    .append("      </div>\n")
                    .append("    </div>\n");

        return headerBuilder.toString();
    }

    /**
     * 获取默认的页脚 HTML。
     *
     * @return 页脚 HTML
     */
    protected String getDefaultFooter() {
        return "    <div style=\"text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #777777;\">\n" +
               "      <p>此邮件由系统自动发送，请勿直接回复。</p>\n" +
               "      <p>&copy; " + java.time.Year.now().getValue() + " 南风工具箱。保留所有权利。</p>\n" +
               "    </div>\n" +
               "  </div>\n" +
               "</div>";
    }

    /**
     * 将内容包装为完整的 HTML。
     *
     * @param content 内容部分 HTML
     * @return 完整的 HTML
     */
    protected String wrapContent(String content) {
        return getDefaultHeader() + content + getDefaultFooter();
    }

    /**
     * 设置 LOGO 的 URL。
     *
     * @param logoUrl LOGO 的 URL
     * @return 当前实例，用于链式调用。
     */
    public AbstractMailTemplate setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
        return this;
    }

}