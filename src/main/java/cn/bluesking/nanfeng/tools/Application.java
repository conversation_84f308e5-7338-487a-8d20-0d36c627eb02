package cn.bluesking.nanfeng.tools;

import java.io.InputStream;
import java.util.Locale;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.PropertySource;

import com.aspose.email.License;

/**
 * 启动程序。
 * <p>应用程序入口类，负责初始化 Aspose Email 许可证并启动 Spring Boot 应用。
 *
 * <AUTHOR>
 * @date 2024-02-29
 * @since 1.0.0
 */
@SpringBootApplication
@PropertySource(value = "file:${SPRING_CONFIG_LOCATION}", encoding = "UTF-8")
public class Application {

    /*-------------------- public static method --------------------*/

    public static void main(String[] args) {
        authorizeAsposeEmailLicense();
        SpringApplication.run(Application.class, args);
    }

    /*-------------------- private static method --------------------*/

    private static void authorizeAsposeEmailLicense() {
        Locale locale = new Locale("zh", "cn");
        Locale.setDefault(locale);

        InputStream is = Application.class.getResourceAsStream("/aspose_total_20991231_license.xml");
        License license = new License();
        license.setLicense(is);
    }

}